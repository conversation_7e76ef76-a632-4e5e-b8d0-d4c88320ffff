
server
{
    listen 80;
    server_name local.soulyc.com;
    index index.php;
    root /var/www/html/yaf-soul-yc/public;

    location / {
        try_files $uri $uri/ /index.php;
    }

    location = /api.php {
        include fastcgi_params;
        fastcgi_pass fpm-server:9000;  # 确保这里的配置与你的 PHP-FPM 设置匹配
        fastcgi_index api.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    location = /d.php {
        include fastcgi_params;
        fastcgi_pass fpm-server:9000;  # 确保这里的配置与你的 PHP-FPM 设置匹配
        fastcgi_index d.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    include enable-php-pathinfo.conf;
    include static-cached.conf;
}

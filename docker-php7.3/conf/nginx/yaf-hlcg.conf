# caoliu web

server
{
    listen 80 ;
    server_name hlcg.local;
    index index.php;
    root  /var/www/html/yaf-hlcg-app/public;

    location / {
        try_files $uri $uri/ /index.php$uri$is_args$args;
    }

    location ~ [^/]\.php(/|$)
    {
        include fastcgi_params;
        fastcgi_pass fpm-server:9000;
        include fastcgi.conf;
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # include enable-php-pathinfo.conf;
    include static-cached.conf;

}

server {
    listen 80;
    server_name caoliuadmin.local;
    index d.php;
    root /var/www/html/yaf-hlcg-app/public;

    location / {
        try_files $uri $uri/ /d.php$uri$is_args$args;
    }

    location ~ [^/]\.php(/|$)
    {
        include fastcgi_params;
        fastcgi_pass fpm-server:9000;
        include fastcgi.conf;
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    include static-cached.conf;
}
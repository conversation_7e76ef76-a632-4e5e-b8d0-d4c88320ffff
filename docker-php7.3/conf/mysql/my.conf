# Copyright (c) 2017, Oracle and/or its affiliates. All rights reserved.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; version 2 of the License.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301 USA

#
# The MySQL  Server configuration file.
#
# For explanations see
# http://dev.mysql.com/doc/mysql/en/server-system-variables.html

[mysqld]
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
datadir         = /var/lib/mysql
secure-file-priv=NULL
user=mysql
# Disabling symbolic-links is recommended to prevent assorted security risks
# symbolic-links=0
default-authentication-plugin=mysql_native_password
# Custom config should go here
!includedir /etc/mysql/conf.d/

log_output = file
slow_query_log = 1
slow_query_log_file = /log/mysql-slow.log
long_query_time = 1

# lower_case_table_names = 2


# skip-grant-tables

ft_min_word_len = 2
innodb_ft_min_token_size = 2


server_id = 2
log-bin = mysql-bin
slave_skip_errors=all
sync_binlog=0
innodb_flush_log_at_trx_commit=0
replicate-do-db = kk
slave-net-timeout = 60
log_bin_trust_function_creators = 1
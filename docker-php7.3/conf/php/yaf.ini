[yaf]
yaf.environ = develop
yaf.use_namespace = on
yaf.forward_limit = 20
yaf.use_spl_autoload = 1

[yac]
yac.enable = 1
yac.enable_cli = 1
yac.keys_memory_size = 32M
yac.values_memory_size = 512M

[XDebug]
# sessionkey
xdebug.idekey='PHPSTORM'
# 开启远程调试
# xdebug.remote_enable=1
# 远程调试通信端口
# xdebug.remote_port=9000
# 远程调试通信端口
;xdebug.remote_host=**********
# xdebug.remote_host=docker.for.mac.localhost
# xdebug.remote_autostart = 1
# xdebug.remote_connect_back = 0

xdebug.start_with_request = yes
xdebug.discover_client_host=0
xdebug.mode = debug
xdebug.client_host = host.docker.internal
xdebug.client_port = 9003
xdebug.collect_return=On ;收集返回值
xdebug.log="/tmp/xdebug.log"

[xhprof]
# opcache.enable=1
xhprof.output_dir=/var/www/html/xhprof/log

[opcache]
;开启opcache
opcache.enable=1

;CLI环境下，PHP启用OPcache
opcache.enable_cli=0

;OPcache共享内存存储大小,单位MB
opcache.memory_consumption=128

;PHP使用了一种叫做字符串驻留（string interning）的技术来改善性能。例如，如果你在代码中使用了1000次字符串“foobar”，在PHP内部只会在第一使用这个字符串的时候分配一个不可变的内存区域来存储这个字符串，其他的999次使用都会直接指向这个内存区域。这个选项则会把这个特性提升一个层次——默认情况下这个不可变的内存区域只会存在于单个php-fpm的进程中，如果设置了这个选项，那么它将会在所有的php-fpm进程中共享。在比较大的应用中，这可以非常有效地节约内存，提高应用的性能。
;这个选项的值是以兆字节（megabytes）作为单位，如果把它设置为16，则表示16MB，默认是4MB
opcache.interned_strings_buffer=8

;这个选项用于控制内存中最多可以缓存多少个PHP文件。这个选项必须得设置得足够大，大于你的项目中的所有PHP文件的总和。
;设置值取值范围最小值是 200，最大值在 PHP 5.5.6 之前是 100000，PHP 5.5.6 及之后是 1000000。也就是说在200到1000000之间。
opcache.max_accelerated_files=4000

;设置缓存的过期时间（单位是秒）,为0的话每次都要检查
opcache.revalidate_freq=60

;从字面上理解就是“允许更快速关闭”。它的作用是在单个请求结束时提供一种更快速的机制来调用代码中的析构器，从而加快PHP的响应速度和PHP进程资源的回收速度，这样应用程序可以更快速地响应下一个请求。把它设置为1就可以使用这个机制了。
opcache.fast_shutdown=1

;如果启用（设置为1），OPcache会在opcache.revalidate_freq设置的秒数去检测文件的时间戳（timestamp）检查脚本是否更新。
;如果这个选项被禁用（设置为0），opcache.revalidate_freq会被忽略，PHP文件永远不会被检查。这意味着如果你修改了你的代码，然后你把它更新到服务器上，再在浏览器上请求更新的代码对应的功能，你会看不到更新的效果
;强烈建议你在生产环境中设置为0，更新代码后，再平滑重启PHP和web服务器。
opcache.validate_timestamps=0

;开启Opcache File Cache(实验性), 通过开启这个, 我们可以让Opcache把opcode缓存缓存到外部文件中, 对于一些脚本, 会有很明显的性能提升.
;这样PHP就会在/tmp目录下Cache一些Opcode的二进制导出文件, 可以跨PHP生命周期存在.
opcache.file_cache=/tmp


[scws]
; 注意请检查 php.ini 中的 extension_dir 的设定值是否正确, 否则请将 extension_dir 设为空，
; 再把 extension = scws.so 或 php_scws.dll 指定绝对路径。
; extension = scws.so
scws.default.charset = utf-8
### 注意事项

> 项目目录应该放在当前目录的上一级目录 `../` 

##### 目录示例
```
(base) ➜  www tree -L 1 .
.
├── docker          # docker-compose 目录，也就是本 docker-compose 的目录
├── yaf-aimei       # 项目目录 1
├── yaf-awjq        # 其他项目目录 .....
├── yaf-cartoon     
├── yaf-data-bot
└── zpcweb
```

> 主环境版本： `mysql 8.0` `redis 8.0.2` `php 7.3`

> php扩展版本  `yaf-3.0.8` `yac-2.3.1` `xhprof-2.3.10` `xdebug-3.1.5` `redis-6.2.0`  

> mysql 密码  `root`  


##### 使用说明： `` 
> 在docker的高版本中 `docker-compose`命令 被更换成了 `docker compose`  
> `docker-compose up -d` 拉取、升级、运行服务  
> `docker-compose start` 启动所有服务  
> `docker-compose stop` 停止所有服务  
> `docker-compose restart` 重启所有服务  
> `docker-compose down` 卸载所有服务  

##### 目录说明
```
.
├── docker-compose.yml
├── conf
│   ├── mysql
│   ├── nginx
│   │   └── www.conf      # nginx的域名配置
│   └── php
│       ├── php-fpm.conf  # fpm 配置
│       └── yaf.ini       # php扩展配置
├── data        # mysql和es 的数据目录
├── fpm         # fpm容器
├── logs        # 日志目录
└── readme.md
```

#version: "3.9"

networks:
  hyperf_net:
    external: true

services:
  nginx-server:
    networks:
      - hyperf_net
    container_name: nginx-tmp
    image: nginx
    volumes:
      - ../:/var/www/html
      - ./logs/nginx:/var/log/nginx
      - ./conf/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./conf/nginx/enable-php.conf:/etc/nginx/enable-php.conf
      - ./conf/nginx/enable-php-pathinfo.conf:/etc/nginx/enable-php-pathinfo.conf
      - ./conf/nginx/pathinfo.conf:/etc/nginx/pathinfo.conf
      - ./conf/nginx/fastcgi.conf:/etc/nginx/fastcgi.conf
      - ./conf/nginx/static-cached.conf:/etc/nginx/static-cached.conf
      - ./conf/nginx/www.conf:/etc/nginx/conf.d/default.conf
      - ./conf/nginx/yaf-hlcg.conf:/etc/nginx/conf.d/yaf-hlcg.conf
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=0.0.0.0
      - NGINX_PORT=80
    links:
      - fpm-server
      ## - cli-server
  db-server:
    container_name: db
    image: mysql:8.0
    volumes:
      - ./conf/mysql/my.conf:/etc/mysql/my.cnf
      - ./data/mysql:/var/lib/mysql
      - ./logs/mysql:/log
    environment:
      - MYSQL_ROOT_PASSWORD=root
    ports:
      - "3306:3306"
    networks:
      - hyperf_net
  redis-server:
    container_name: redis
    image: redis:8.0.2-alpine
    ports:
      - "6379:6379"
    networks:
      - hyperf_net
  fpm-server:
    container_name: fpm
    build: ./fpm
    networks:
      - hyperf_net
    volumes:
      - ../:/var/www/html
      - ./conf/php/php.ini:/usr/local/etc/php/php.ini
      - ./conf/php/yaf.ini:/usr/local/etc/php/conf.d/yaf.ini
      - ./conf/php/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ./conf/php/php-fpm.conf:/usr/local/etc/php-fpm.d/www.conf
      - ./logs/fpm:/var/log
    environment:
      - XDEBUG_CONFIG=client_host=********** client_port=9003 idekey=VSCODE
    links:
      - redis-server
      - db-server
      - es-server
    ports:
      - "22280:22280"
  es-server:
    container_name: es
    image: 'docker.elastic.co/elasticsearch/elasticsearch:7.16.0'
    networks:
      - hyperf_net
    environment:
      - "discovery.type=single-node"
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - ./data/es:/usr/share/elasticsearch/data

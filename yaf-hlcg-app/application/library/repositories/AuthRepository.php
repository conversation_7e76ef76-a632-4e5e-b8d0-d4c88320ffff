<?php

namespace repositories;


use tools\RedisService;
use Yaf\Session;

class AuthRepository
{
    /**
     * 获取用户信息
     * @return bool|mixed
     */
    public static function getManager()
    {
        $user = Session::getInstance()->get('manager');
        if ($user['lastip'] != client_ip()) {
            return false;
        }
        if (TIMESTAMP - $user['lastvisit'] > 7200) {
            return false;
        }
        return $user;
    }

    /**
     * login out
     * @return bool
     */
    public static function handleLoginOut()
    {
        Session::getInstance()->del('manager');
        return true;
    }
}
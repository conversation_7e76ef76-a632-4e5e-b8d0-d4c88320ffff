<?php

namespace service;

use CURLFile;
use FaceCateModel;
use FaceMaterialModel;
use MemberFaceModel;
use MemberModel;
use MemberStripModel;
class AiService
{
    public function getStripPreData($member){
        $free_num = $member["ty_times"]??0;
        return [
            'max_size'       => '2M',
            'free_num'       => (string)$free_num,
            'coins'          => $member["coins"],
            'times'          => '60',
            'ai_ty_coins'    => setting('ai_ty_coins', 9),
            'ai_ty_tips'     => setting('ai_ty_tips', '1、素材仅供AI使用，绝无外泄风险，请放心使用。##2、素材需清晰，不超过2MB，上传间隔大于60秒。##3、本功能不支持多人图片，脸部无遮挡物（眼镜、刘海等）。##4、生成失败返回免费次数，若违规则免费次数作废。##5、近距离大头照会生成失败，禁止使用未成年人照片！'),
            'exp_before_img' => url_image('/upload/ads/20240212/2024021214382420073.png'),
            'exp_after_img'  => url_image('/upload/ads/20240212/2024021214390042872.png'),
        ];
    }

    //检查图片格式
    protected function check_type($file)
    {
        $mime_types = [
            'png' => 'image/png',
            'jpe' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'ico' => 'image/vnd.microsoft.icon',
            'tiff' => 'image/tiff',
            'tif' => 'image/tiff',
            'svg' => 'image/svg+xml',
            'svgz' => 'image/svg+xml',
        ];
        $temp = explode('.', $file);
        $ext = strtolower(array_pop($temp));
        if (array_key_exists($ext, $mime_types)) {
            return $mime_types[$ext];
        }else{
            test_assert(false, '仅支持JPEG|JPG|PNG|GIF|BMP|WEBP|AVIF图片格式,其他格式请自行转码');
        }
        $url = TB_IMG_ADM_US . $file;
        $image = file_get_contents($url);
        test_assert($image, '请求远程异常:' . $url);
        $md5 = substr(md5($url), 0, 16);
        $from = APP_PATH . '/storage/data/images/' . $md5 . '_fr';
        $dirname = dirname($from);
        if (!is_dir($dirname) || !file_exists($dirname)) {
            mkdir($dirname, 0755, true);
        }
        $rs = file_put_contents($from, $image);
        test_assert($rs, '无法写入文件:' . $from);
        $cover = new CURLFile(realpath($from), mime_content_type($from));
        test_assert($cover, '仅支持JPEG|JPG|PNG|GIF|BMP|WEBP|AVIF图片格式,其他格式请自行转码');
        //删除图片
        unlink($from);
        if (!in_array($cover->mime,  ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/avif'])){
            test_assert(false, '仅支持JPEG|JPG|PNG|GIF|BMP|WEBP|AVIF图片格式,其他格式请自行转码');
        }
    }



    public function strip($member, $thumb, $thumb_w, $thumb_h)
    {
        //检查格式
        $this->check_type($thumb);

        transaction(function () use ($member, $thumb, $thumb_w, $thumb_h) {
            $ai_pay = MemberStripModel::AI_PAY_TYPE_COINS;//AI支付 1VIP免费次数 2金币
            $has = $member["ty_times"]??0;
            $need_coins = setting('ai_ty_coins',9);
            if ($has>0){
                $ai_pay = MemberStripModel::AI_PAY_TYPE_FREE;
            }
            if ($ai_pay == MemberStripModel::AI_PAY_TYPE_COINS && $member["money"] < $need_coins) {
                throw new \Exception('余额不足');
            }
            # 新增记录
            $rs = MemberStripModel::create_record($member["aff"], $thumb, $thumb_w, $thumb_h, $ai_pay, $need_coins);
            test_assert($rs, '系统异常，请稍后再试');
            $member = MemberModel::where("aff",$member["aff"])->first();
            if ($ai_pay == MemberStripModel::AI_PAY_TYPE_FREE){ // 减少免费次数
                $isOk = $member->decrement('ty_times');
                test_assert($isOk, '扣除免费次数,请确认你的vip等级');
            }else{ // 减少金币
                $logDesc = "脱衣扣除金币{$need_coins}。";
                $isOk =  $member->subMoney($need_coins, \MoneyLogModel::SOURCE_BUY_STRIP, $logDesc);
                test_assert($isOk, '扣款失败,请确认您的余额是否足够');
                //记录日志
            }
            $ty_id = $rs->id;
            bg_run(function () use ($ty_id){
               AiSdkService::strip($ty_id);
           });
        });
    }

    public function list_my_strip($member, $status, $page, $limit)
    {
        return MemberStripModel::list_my_strip($member["aff"], $status, $page, $limit);
    }

    public function list_face_nav()
    {
        return FaceCateModel::list_cate();
    }


    public function getFacePreData($member){
        $free_num = $member["change_face_times"]??0;
        return [
            'max_size'       => '2M',
            'free_num'       => $free_num,
            'coins'          => $member["coins"],
            'ai_ht_coins'    => setting('ai_ht_coins', 9),
            'ai_ht_tips'     => setting('ai_ht_tips', '1、素材仅供AI使用，绝无外泄风险，请放心使用。##2、素材需清晰，不超过2MB，上传间隔大于60秒。##3、本功能不支持多人图片，脸部无遮挡物（眼镜、刘海等）。##4、生成失败返回免费次数，若违规则免费次数作废。##5、近距离大头照会生成失败，禁止使用未成年人照片'),
            'exp_correct_img'=> url_image('/upload/ads/20240416/2024041620214356903.png'),
            'exp_error1_img' => url_image('/upload/ads/20240416/2024041620223196413.png'),
            'exp_error2_img' => url_image('/upload/ads/20240416/2024041620221793507.png'),

        ];
    }

    public function change_face($member, $material_id, $thumb, $thumb_w, $thumb_h,$ground,$ground_h,$ground_w)
    {


        transaction(function () use ($member, $material_id, $thumb, $thumb_w, $thumb_h,$ground,$ground_h,$ground_w) {
            $material = $material_id ? FaceMaterialModel::get_detail($material_id) : null;
            //test_assert($material, '素材已被删除');
//            if ($material->type == 0){
//                $need_coins = $material->coins;
//                if (!$need_coins){
//                    $need_coins = setting('ai_ht_coins',9);
//                }
//                if ($member["coins"] < $need_coins) {
//                    errLog($member["coins"]."---".$need_coins);
//                    throw new \Exception('余额不足');
//                }
//
//                $isOk = MemberModel::where('aff', $member["aff"])
//                    ->where('coins', '>=', $need_coins)
//                    ->decrement('coins', $need_coins);
//                test_assert($isOk, '扣款失败,请确认您的余额是否足够');
//
//                $rs = MemberFaceModel::create_record($member["aff"], $material_id, 1, $need_coins, $material->thumb, $material->thumb_w, $material->thumb_h, $thumb, $thumb_w, $thumb_h);
//                test_assert($rs, '系统异常，请稍后再试');
//                $this->userGoldChange($member["uuid"], $need_coins, '[AI换脸]扣款', '你使用了一次AI换脸', 2, $material_id);
//            }else{
                if ($material){
                    $ground = $material->thumb;
                    $ground_h = $material->thumb_h;
                    $ground_w = $material->thumb_w;
                }
                if ($member["change_face_times"] <= 0){
                    $need_coins = $material ? $material->coins : 0;
                    if (!$need_coins){
                        $need_coins = setting('ai_ht_coins',9);
                    }
                    if ($member["money"] < $need_coins) {
                        errLog($member["money"]."---".$need_coins);
                        throw new \Exception('余额不足');
                    }

//                    $isOk = MemberModel::where('aff', $member["aff"])
//                        ->where('money', '>=', $need_coins)
//                        ->decrement('money', $need_coins);
                    $member = MemberModel::where("aff",$member["aff"])->first();
                    $logDesc = "换脸扣除金币{$need_coins}。";
                    $isOk = $member->subMoney($need_coins, \MoneyLogModel::SOURCE_BUY_FACE, $logDesc);
                    test_assert($isOk, '扣款失败,请确认您的余额是否足够');
                    $rs = MemberFaceModel::create_record($member["aff"], $material_id, 1, $need_coins, $ground, $ground_w, $ground_h, $thumb, $thumb_w, $thumb_h);
                }else{
                    $isOk = MemberModel::where('aff', $member["aff"])
                        ->where('change_face_times', '>=', 1)
                        ->decrement('change_face_times');
                    test_assert($isOk, '扣除解锁次数不足');
                    $rs = MemberFaceModel::create_record($member["aff"], $material_id, 0, 0, $ground, $ground_w, $ground_h, $thumb, $thumb_w, $thumb_h);
                    test_assert($rs, '系统异常，请稍后再试');
                }
           // }
            // 使用次数维护
            $material && $material->increment('used_ct');
            //test_assert($isOk, '系统异常,请稍后重试');
            $ty_id = $rs->id;
            bg_run(function () use ($ty_id){
                AiSdkService::image_face($ty_id);
            });
        });
    }

    public function list_my_face($member, $status, $page, $limit)
    {
        return MemberFaceModel::list_my_face($member["aff"], $status, $page, $limit);
    }
}
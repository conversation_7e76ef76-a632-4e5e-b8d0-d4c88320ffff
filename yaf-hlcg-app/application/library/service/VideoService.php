<?php
namespace service;

use tools\HttpCurl;

class VideoService
{
    const ORDER_LIST = [
        'week_hot' =>  ['name' => '近期最佳',  'order' => 'week_hot'],
        'update'   =>  ['name' => '最近更新',  'order' => 'update'],
        'view'     =>  ['name' => '最多观看',  'order' => 'view'],
        'collect'  =>  ['name' => '最多收藏',  'order' => 'collect'],
    ];

    const AV_ACTOR_ORDER = [
        'hot_num'    => ['name' => '热度优先', 'order' => 'hot_num'],
        'name'       => ['name' => '名称排序', 'order' => 'name'],
        'updated_at' => ['name' => '最近更新', 'order' => 'updated_at'],
        'video_num'  => ['name' => '最多影片', 'order' => 'video_num'],
    ];

    /**
     * av - 女优列表
     * @param $order
     * @param $page
     * @param $limit
     * @return array
     */
    public static function actorList($order, $page, $limit)
    {
        $allList = \AvActorsModel::allList(-1, $order);
        return CommonService::getPageList($allList, $page, $limit);
    }

    /**
     * av - 标签
     * @return array
     */
    public static function tagList()
    {
        return \AvTagModel::getTagList();
    }

    /**
     *  av - 主题，女优，标签 视频列表
     * @param $id
     * @param $field
     * @param $order
     * @param $page
     * @param $limit
     * @return \Illuminate\Support\Collection|mixed
     */
    public static function videoList($id, $field, $order, $page, $limit)
    {
        if(!in_array($field, ['actor_ids', 'theme_ids', 'tag_ids']))
        {
            test_assert(true, '排序异常');
        }

        if(!in_array($order, array_keys(self::ORDER_LIST)))
        {
            test_assert(true, '排序异常');
        }


        $list   = \VideosModel::avList($id, $order, $page, $limit, $field);
        $detail = self::getModelByField($field)->where(['id' => $id])->first();
        $detail = [
            'id'        => $detail['id'] ?? 0,
            'name'      => $detail['name'] ?? '',
            'video_num' => $detail['video_num'] ?? 0,
            'cover'     => isset($detail['img']) ? url_image($detail['img']) : ''
        ];

        return [$list, $detail];
    }


    private static function getModelByField($field)
    {
        if(empty($field))
        {
            return null;
        }

        $model = null;
        switch ($field)
        {
            case 'theme_ids':
                $model = new \AvThemeModel();
                break;
            case 'actor_ids':
                $model = new \AvActorsModel();
                break;
            case 'tag_ids':
                $model = new \AvTagModel();
                break;
        }

        return $model;
    }

    public static function videoOrderList($order, $page, $limit)
    {
        if(!in_array($order, ['play', 'week_hot', 'update', 'month_hot', 'view']))
        {
            test_assert(true, '排序异常');
        }

        return \VideosModel::avList(0, $order, $page, $limit);
    }
    public static function theme()
    {
        // 主题列表
        $res['theme'] = \AvThemeModel::allList(-1);

        // 正在观看 本周最热 最近更新 本月热门 总热门
        $res['list'] = [];

        array_push($res['list'], [
            'name'  => '正在观看',
            'order' => 'play',
            'list'  => \VideosModel::avList(0, 'play', 1, 10)
        ]);

        array_push($res['list'], [
            'name'  => '本周最热',
            'order' => 'week_hot',
            'list'  => \VideosModel::avList(0, 'week_hot', 1, 10)
        ]);

        array_push($res['list'], [
            'name'  => '最近更新',
            'order' => 'update',
            'list'  => \VideosModel::avList(0, 'update', 1, 10)
        ]);

        array_push($res['list'], [
            'name'  => '本月热门',
            'order' => 'month_hot',
            'list'  => \VideosModel::avList(0, 'month_hot', 1, 10)
        ]);

        array_push($res['list'], [
            'name'  => '总热门',
            'order' => 'view',
            'list'  => \VideosModel::avList(0, 'view', 1, 10)
        ]);

        return $res;
    }

    /**
     * api 接口请求签名验签
     * @param $data
     * @return string
     */
    public static function getSign($data): string
    {
        if(isset($data['sign']))
        {
            unset($data['sign']);
        }

        $signKey = '132f1537f85scxpcm59f7e318b9epa51';
        ksort($data);
        $string  = '';
        foreach ($data as $key => $datum) {
            if ($datum === '') {
                continue;
            }
            $string .= "{$key}={$datum}&";
        }
        $string .= 'key=' . $signKey;
        return md5($string);
    }

    public static function crontabAvVideo()
    {
        $model = new \AvThemeModel();
        self::crontabAvComment($model, 'theme_ids');
        $model::clearCache();
        trigger_log("crontabAvVideo theme ending...");


        $model = new \AvTagModel();
        self::crontabAvComment($model, 'tag_ids');
        $model::clearCache();
        trigger_log("crontabAvVideo tag ending...");

        $model = new \AvActorsModel();
        self::crontabAvComment($model, 'actor_ids');
        $model::clearCache();
        trigger_log("crontabAvVideo actor ending...");
    }

    private static function crontabAvComment($model, $field): bool
    {
        $result = $model::where([])->select(['id'])->get();
        $result = to_array($result);
        if(empty($result))
        {
            return false;
        }
        
        foreach ($result as $v)
        {
            
            $counts = self::avCommentCount($v['id'], $field);

            if($counts <= 0)
            {
                continue;
            }

            $data = ['video_num' => $counts];

            if($field == 'actor_ids')
            {
                $ctSum = \VideosModel::where([
                    'is_hide' => \VideosModel::IS_SHOW,
                    'status' => \VideosModel::STATUS_PASS
                ])->whereRaw(" FIND_IN_SET('{$v['id']}', actor_ids) > 0 ")->sum('view_ct');
                $ctSum = intval($ctSum);
                if($ctSum > 0)
                {
                    $data['hot_num'] = $ctSum;
                }
            }

            $model::where(['id' => $v['id']])->update($data);
        }

        return true;
    }

    public static function avCommentCount($id, $field): int
    {
        if(empty($id) || empty($field))
        {
            return 0;
        }

        $res = \VideosModel::where([
            'is_hide' => \VideosModel::IS_SHOW,
            'status' => \VideosModel::STATUS_PASS
        ])->whereRaw(" FIND_IN_SET('{$id}', {$field}) > 0 ")->count();

        return intval($res);
    }


}

<?php

namespace Illuminate\Database\Connectors;

use Illuminate\Database\Connectors\Connector;
use Illuminate\Database\Connectors\ConnectorInterface;
use PDO;

/**
 * 修复版本的MySqlConnector，解决 "Cannot execute queries while other unbuffered queries are active" 错误
 */
class MySqlConnector extends Connector implements ConnectorInterface
{
    /**
     * Establish a database connection.
     *
     * @param  array  $config
     * @return \PDO
     */
    public function connect(array $config)
    {
        $dsn = $this->getDsn($config);

        $options = $this->getOptions($config);

        // 强制设置缓冲查询选项来修复错误
        $options[PDO::MYSQL_ATTR_USE_BUFFERED_QUERY] = true;
        $options[PDO::ATTR_EMULATE_PREPARES] = true;
        $options[PDO::MYSQL_ATTR_MULTI_STATEMENTS] = false;
        $options[PDO::ATTR_AUTOCOMMIT] = true;

        // We need to grab the PDO options that should be used while making the brand
        // new connection instance. The PDO options control various aspects of the
        // connection's behavior, and some might be specified by the developers.
        $connection = $this->createConnection($dsn, $config, $options);

        // 关键修复：不执行 "use database" 语句，因为数据库名已经在DSN中指定
        // 原始代码：if (! empty($config['database'])) { $connection->exec("use `{$config['database']}`;"); }
        // 这行代码是导致 "Cannot execute queries while other unbuffered queries are active" 错误的根本原因

        $this->configureIsolationLevel($connection, $config);

        $this->configureEncoding($connection, $config);

        // Next, we will check to see if a timezone has been specified in this config
        // and if it has we will issue a statement to modify the timezone with the
        // database. Setting this DB timezone is an optional configuration item.
        $this->configureTimezone($connection, $config);

        $this->setModes($connection, $config);

        return $connection;
    }

    /**
     * Create a DSN string from a configuration.
     *
     * @param  array  $config
     * @return string
     */
    protected function getDsn(array $config)
    {
        extract($config, EXTR_SKIP);

        // 调试信息
        error_log("MySqlConnector getDsn config: " . json_encode($config));

        // 确保DSN中包含数据库名，这样就不需要后续的 "use database" 语句
        $dsn = "mysql:host={$host}";

        if (isset($port)) {
            $dsn .= ";port={$port}";
        }

        if (isset($database)) {
            $dsn .= ";dbname={$database}";
        }

        // 修复：只有当unix_socket存在且不为空时才使用socket连接
        if (isset($unix_socket) && !empty($unix_socket)) {
            $dsn = "mysql:unix_socket={$unix_socket}";
            if (isset($database)) {
                $dsn .= ";dbname={$database}";
            }
        }

        if (isset($charset)) {
            $dsn .= ";charset={$charset}";
        }

        error_log("MySqlConnector final DSN: " . $dsn);
        return $dsn;
    }

    /**
     * Set the connection character set and collation.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return void
     */
    protected function configureEncoding($connection, array $config)
    {
        if (! isset($config['charset'])) {
            return;
        }

        $connection->prepare(
            "set names '{$config['charset']}'".(
                ! empty($config['collation']) ? " collate '{$config['collation']}'" : ''
            )
        )->execute();
    }

    /**
     * Set the timezone on the connection.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return void
     */
    protected function configureTimezone($connection, array $config)
    {
        if (isset($config['timezone'])) {
            $connection->prepare('set time_zone="'.$config['timezone'].'"')->execute();
        }
    }

    /**
     * Create a new PDO connection instance.
     *
     * @param  string  $dsn
     * @param  string  $username
     * @param  string  $password
     * @param  array  $options
     * @return \PDO
     */
    protected function createPdoConnection($dsn, $username, $password, $options)
    {
        if (version_compare(PHP_VERSION, '5.3.6', '<') && ! empty($options[PDO::MYSQL_ATTR_SSL_CA])) {
            throw new Exception('MySQL SSL connection is not supported on PHP versions below 5.3.6.');
        }

        return new PDO($dsn, $username, $password, $options);
    }

    /**
     * Set the modes for the connection.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return void
     */
    protected function setModes(PDO $connection, array $config)
    {
        if (isset($config['modes'])) {
            $this->setCustomModes($connection, $config);
        } elseif (isset($config['strict'])) {
            if ($config['strict']) {
                $connection->prepare($this->strictMode($connection, $config))->execute();
            } else {
                $connection->prepare("set session sql_mode='NO_ENGINE_SUBSTITUTION'")->execute();
            }
        }
    }

    /**
     * Set the custom modes on the connection.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return void
     */
    protected function setCustomModes(PDO $connection, array $config)
    {
        $modes = implode(',', $config['modes']);

        $connection->prepare("set session sql_mode='{$modes}'")->execute();
    }

    /**
     * Get the query to enable strict mode.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return string
     */
    protected function strictMode(PDO $connection, array $config)
    {
        $version = $config['version'] ?? $connection->getAttribute(PDO::ATTR_SERVER_VERSION);

        if (version_compare($version, '8.0.11') >= 0) {
            return "set session sql_mode='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'";
        }

        return "set session sql_mode='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'";
    }

    /**
     * Configure the connection's isolation level.
     *
     * @param  \PDO  $connection
     * @param  array  $config
     * @return void
     */
    protected function configureIsolationLevel($connection, array $config)
    {
        if (! isset($config['isolation_level'])) {
            return;
        }

        $connection->prepare(
            "SET SESSION TRANSACTION ISOLATION LEVEL {$config['isolation_level']}"
        )->execute();
    }
}

<?php

use Illuminate\Support\Str;
use service\UserService;
use Carbon\Carbon;
use service\ChannelService;
use tools\HttpCurl;

class LibSync
{

    const SYNC_URL = "https://admin.91porn.fun/api";

    /** 视频同步 start */
    public static function syncAv($maxId=0)
    {
        $redis    = redis();
        $redisKey = "web:sync_av2";
        if($maxId == 0 && $redis->exists($redisKey))
        {
            $maxId = $redis->get($redisKey);
        }

        $limit   = 100;
        $params  = array_merge(self::createSign(['xxx' => 'aaa']), [
            'max_id' => $maxId,
            'limit'  => $limit
        ]);

//        trigger_log("syncAv ... params = ".var_export($params, true));
        $result  = HttpCurl::avGet(self::SYNC_URL . "/avlist", $params);
        $result  = !empty($result) ? json_decode($result, true) : [];
        $list    = isset($result['data']) ? $result['data'] : [];
//        trigger_log("syncAv ... result = ".var_export($list, true));

        if(empty($list))
        {
            return false;
        }

        foreach ($list as $v)
        {
            self::syncAvDetail($v);
        }

        $maxId = max(array_column($list, 'id'));
        $redis->set($redisKey, $maxId);

        trigger_log("syncAv ... 开始执行下一轮 执行开始id为 {$maxId}");
        self::syncAv($maxId);
    }

    protected static function syncAvDetail($video)
    {
        $vid    = "{$video['id']}";
        $number = str_replace('av_', '', $video['vid']);

        $detail = \VideosModel::where(['_id' => $vid, '_id_hash' => $number])->first(['id']);
        if(!empty($detail))
        {
            trigger_log("syncAvDetail ... {$detail['id']} 已存在...");
            return false;
        }

        $cover      = !empty($video['cover']) ? $video['cover'] : $video['first_img'] ?? '';
        $hot        = rand(15000, 50000);
        $addData    = [
            '_id'          => $vid,
            '_id_hash'     => $number,
            'source_240'   => $video['video_url'],
            'title'        => trim(str_replace($number, '', $video["video_title"])),
            'cover_thumb'  => $cover,
            'duration'     => $video['video_duration'],
            'v_ext'        => "v_ext",
            'video_type'   => 2,
            'status'       => 1,
            'directors'    => "",
            'publisher'    => "",
            'via'          => "avsync",
            'coins'        => 0,
            'like_ct'      => rand(500, 5000),
            'favorite_ct'  => rand(50, 15000),
            'favorite_fct' => rand(500, 5000),
            'view_ct'      => $hot,
            'view_fct'     => $hot,
            'play_at'      => $hot,
            'view_week'    => $hot,
            'view_month'   => $hot,
            'created_at'   => TIMESTAMP,
            'updated_at'   => TIMESTAMP,
            'refresh_at'   => TIMESTAMP
        ];
//            trigger_log('addVideoForAv add data '.var_export($addData, true));
        $videoId     = \VideosModel::insertGetId($addData);
        if(empty($videoId) || $videoId == 0)
        {
            return false;
        }

        // av 女优绑定关联
        $saveData = [];
        if(!empty($video['actors']))
        {
            $saveData['actor_ids'] = self::bindVideoRel($video['actors'], new \AvActorsModel());
        }

        if(!empty($video['tags']))
        {
            $saveData['tag_ids'] = self::bindVideoRel($video['tags'], new \AvTagModel());
        }

        if(!empty($video['theme']))
        {
            $saveData['theme_ids'] = self::bindVideoRel($video['theme'], new \AvThemeModel());
        }

        if(!empty($saveData))
        {
//            trigger_log("syncAvDetail save data ...".var_export($saveData, true));
            \VideosModel::where(['id' => $videoId])->update($saveData);
        }

        return true;
    }

    public static function bindVideoRel($nameList, $model): string
    {

        if(empty($nameList))
        {
            return '';
        }

        $rtn  = '';

        trigger_log(" bind video rel 1...".count($nameList));
        foreach ($nameList as $val) {
            $detail = $model->where(['name' => $val])->first(['id']);
            if(empty($detail))
            {
                $insertData = [
                    'name'     => $val['name'],
                    'is_index' => $val['is_index']
                ];
                if(isset($val['img_path']) && !empty($val['img_path']))
                {
                    $insertData['img'] = $val['img_path'];
                }

                $detailId = $model::insertGetId($insertData);
            }else{
                $detailId = $detail['id'];
            }

            $rtn .= "{$detailId},";
        }


        return trim($rtn, ',');
    }
    /** 视频同步 end */

    /** 帖子同步 start */
    public static function syncMelon()
    {
        $i     = 1;
        $total = 5000;
        do{
            $params  = array_merge(self::createSign([
                'id' => $i
            ]));

//        trigger_log("syncAv ... params = ".var_export($params, true));
            $result  = HttpCurl::avGet(self::SYNC_URL . "/mdetail", $params);
            $result  = !empty($result) ? json_decode($result, true) : [];
            $i++;

            $data = $result['data'] ?? [];

            if(empty($data)){
                continue;
            }
//            if(isset($result['data']['max_id']) && $result['data']['max_id'] > $total)
//            {
//                $total = $result['data']['max_id'];
//            }

            self::syncMelonHandle($data);

            trigger_log(" syncMelon end 执行完 {$i}... ");

        }while($i<=$total);

        return true;
    }

    private static function syncMelonHandle($melon): bool
    {
        $detail = \ContentsModel::where(['tid' => $melon['id']])->select(['tid'])->first();
        if(!empty($detail))
        {
            return true;
        }

        $cateName = $melon['cate_name'] ?? '';
        $cateIds  = self::syncMelonMetas($cateName, \MetasModel::TYPE_CATEGORY);
        $tags     = $melon['tags'] ?? '';
        $tagIds   = self::syncMelonMetas($tags, \MetasModel::TYPE_TAG);

        $contentId = \ContentsModel::insertGetId([
            'tid'          => $melon['id'],
            'cate_ids'     => $cateIds,
            'tag_ids'      => $tagIds,
            'title'        => $melon['title'] ?? '',
            'order'        => $melon['sort'] ?? '',
            'view'         => $melon['hot_num'],
            'cover'        => $melon['old_cover'],
            'text'         => $melon['content'] ?? '',
            'created'      => !empty($melon['created_at']) ? strtotime($melon['created_at']) : time(),
            'modified'     => !empty($melon['publish_time']) ? strtotime($melon['publish_time']) : time(),
            'authorId'     => 9,
            'allowComment' => 1,
            'is_home'      => 1,
            'allowPing'    => 0,
            'type'         => \ContentsModel::TYPE_POST,
            'status'       => \ContentsModel::STATUS_PUBLISH,
        ]);

        $cateIdArr = !empty($cateIds) ? explode(',', $cateIds) : [];
        if(!empty($cateIdArr))
        {
            foreach ($cateIdArr as $cateId)
            {
                \RelationshipsModel::insert([
                    'cid' => $contentId,
                    'mid' => $cateId,
                ]);
            }
        }

        return true;
    }

    private static function syncMelonMetas($names, $type): string
    {
        $names = explode( ',', $names);
        $ids   = '';
        foreach ($names as $name)
        {
            if(empty($name) || $name == '')
            {
                continue;
            }

            $detail = \MetasModel::where(['name' => $name, 'type' => $type])->select(['mid'])->first();
            if(!empty($detail))
            {
                $id = $detail['mid'];
            }else{
                $id = \MetasModel::insertGetId([
                    'name' => $name,
                    'slug' => $name,
                    'type' => $type,
                ]);
            }

            $ids .= "{$id},";
        }

        return trim($ids, ',');
    }
    /** 帖子同步 end */

    public static function createSign($params)
    {
        return array_merge($params, ['sign' => self::getSign($params)]);
    }

    /**
     * api 接口请求签名验签
     * @param $data
     * @return string
     */
    public static function getSign($data): string
    {
        if(isset($data['sign']))
        {
            unset($data['sign']);
        }
//        trigger_log(" get sign data...".var_export($data, true));
        $signKey = '132f1537f85scxpcm59f7e318b9epa51';
        ksort($data);
        $string  = '';
        foreach ($data as $key => $datum) {
            if ($datum === '') {
                continue;
            }
            $string .= "{$key}={$datum}&";
        }
        $string .= 'key=' . $signKey;
        return md5($string);
    }


    private static function repairVideoUrl($url, $videoPrefix): string
    {
        $url = "{$videoPrefix}/".trim($url, '/');
        $hash = bdHash($url,parse_url($url,PHP_URL_PATH));
        return "{$url}?{$hash}";
    }
}

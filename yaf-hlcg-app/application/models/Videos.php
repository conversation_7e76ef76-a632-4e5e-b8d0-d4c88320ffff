<?php


/**
 * class VideosModel
 * 
 * 
 * @property int $id  
 * @property string $_id 影片名字,番号 
 * @property string $_id_hash 三方平台id 
 * @property string $user_uuid 用户UUID 
 * @property string $title 影片标题 
 * @property string $source_240 影片资源1 
 * @property string $source_480 影片资源2 
 * @property string $source_720 影片资源3 
 * @property string $source_1080 影片资源4 
 * @property string $v_ext 视频格式类型 
 * @property int $duration 时长，秒 
 * @property string $cover_thumb 封面小图 
 * @property int $thumb_width 封面宽 
 * @property int $thumb_height 封面高 
 * @property string $directors 导演 
 * @property string $publisher 上传者 
 * @property string $actors 演员 
 * @property string $via 来源 
 * @property int $rating 总历史点击数 
 * @property int $play_count 总的播放量(假数据) 
 * @property int $created_at 创建时间 
 * @property int $refresh_at 刷新时间 
 * @property int $isfree 是否限免 0 收费 1 限免 
 * @property int $like 喜欢点击数 
 * @property int $comment 评论数 
 * @property int $status 状态，0 未审核 1通过 
 * @property int $thumb_duration 精彩时长：秒 
 * @property int $is_hide 0显示1隐藏 
 * @property int $coins 定价 
 * @property string $descriptions 简介 
 * @property int $is_original 1非原创2原创 
 * @property string $preview_source 预览链接 
 * @property int $is_best 置精
 *
 * 
 *
 * @mixin \Eloquent
 */
class VideosModel extends BaseModel
{
    protected $table = 'videos';
    const ES_INDEX_NAME = "qbz_video";
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $fillable = [
        'id', '_id', '_id_hash', 'user_uuid', 'title', 'source_240', 'source_480',
        'source_720', 'source_1080', 'v_ext', 'duration', 'cover_thumb', 'thumb_width',
        'thumb_height', 'directors', 'publisher', 'actors', 'via', 'rating', 'play_count',
        'created_at', 'refresh_at', 'isfree', 'status', 'thumb_duration', 'is_hide', 'coins',
        'descriptions', 'is_original', 'preview_source', 'type', 'is_best','like_at','view_ct',
        'favorite_ct','favorite_fct','view_fct','comment_ct',
        'video_type', 'actor_ids', 'tag_ids', 'theme_ids', 'play_at', 'view_week', 'view_month'
    ];
    protected $guarded = 'id';
    protected $hidden = ['source_480','source_720','source_1080'];

    const TYPE_SHORT = 1;
    const TYPE_LONG = 2;
    const TYPE = [
        self::TYPE_SHORT => '短视频',
        self::TYPE_LONG  => '电影',
    ];
    const IS_HIDE = 1;
    const IS_SHOW = 0;
    const SHOW_STATUS = [
        self::IS_HIDE => '隐藏',
        self::IS_SHOW => '显示'
    ];

    const SORT_SECOND_VIDEO = [
        ['name' => '最新', 'sort' => 'new'],
        ['name' => '最热', 'sort' => 'hot']
    ];

    const SORT_VIDEO = [
        ['name' => '最新更新', 'sort' => 'new'],
        ['name' => '本周最热', 'sort' => 'week'],
        ['name' => '最多观看', 'sort' => 'view'],
        ['name' => '十分钟以上', 'sort' => 'duration']
    ];

    const IS_ORIGINAL = 1;
    const NOT_ORIGINAL = 0;
    const ORIGINAL_TYPE = [
        self::NOT_ORIGINAL => '非原创',
        self::IS_ORIGINAL  => '原创'
    ];
    const IS_FREE = 1;
    const NOT_FREE = 0;
    const FREE_TYPE = [
        self::IS_FREE => '限免',
        self::NOT_FREE => '收费'
    ];

    const PLAY_MULTIPLE = 9.1;

    const STATUS_WAIT = 0;
    const STATUS_PASS = 1;
    const STATUS_OPTIONS = [
        self::STATUS_WAIT => '待审核',
        self::STATUS_PASS => '审核通过'
    ];
    const VIA_ZPC = "zpc";
    const VIA_DSP = "dsp";
    const VIA_SELF = "self";
    const VIA_OPTIONS = [
        self::VIA_ZPC => '制片厂',
        self::VIA_SELF => '短视频',
        self::VIA_DSP => '本平台上传'
    ];
    const BEST_YES = 1;
    const BEST_NO = 0;
    const BEST_OPTIONS = [
        self::BEST_NO => '否',
        self::BEST_YES => '是'
    ];
    const CN_VIDEOS_LIST = '首页视频列表';
    const CK_VIDEOS_LIST = 'ck:videos:list:%s:%s:%s';
    const GP_VIDEOS_LIST = 'gp:videos:list';

    const CK_PLATE_VIDEOS_LIST = 'ck:plate:videos:list:%s:%s:%s:%s';
    const GP_PLATE_VIDEOS_LIST = 'gp:plate:videos:list';
    const CN_PLATE_V2_VIDEOS_LIST = '分类视频列表';
    const CK_PLATE_V2_VIDEOS_LIST = 'ck:plate:videos:V2:list:%s:%s:%s:%s';
    const GP_PLATE_V2_VIDEOS_LIST = 'gp:plate:videos:V2:list';
    const CK_VIDEO_DETAIL = 'ck:video:detailv2:%s';
    const GP_VIDEO_DETAIL  = 'gp:video:detail';
    const CK_SEARCH_VIDEO_LIST = 'ck:search:video:list:%s:%s:';
    const GP_SEARCH_VIDEOS_LIST = 'gp:search:videos:list';
    const CN_SEARCH_VIDEO_LIST = '搜索页视频列表';
    const CK_LIST_BY_TAG = 'ck:video:list:by:tag:%s:%s:%s:%s';
    const GP_LIST_BY_TAG= 'gp:video:list:by:tag';
    const CN_LIST_BY_TAG = '标签视频';
    protected $appends = ["created_ago"];

    const HIDE_FILEDS = ['source_240','source_480','source_720','source_1080','_id','_id_hash','user_uuid','v_ext',
        'directors','publisher','actors','via','zpc_id','construct_id','is_best','is_notice','preview_source','is_original',
        'descriptions','is_hide','status','view_fct','favorite_fct','rating'];

    public function getCreatedAgoAttribute()
    {
       if (APP_MODULE=="api"){
           return isset($this->attributes['refresh_at']) ? formatTimestamp($this->attributes['refresh_at']) : "";
       }else{
           return "";
       }

    }
    public function getTitleAttribute(): string
    {
         return  str_replace(['"',"'"], ["&quot;","&#39;"],$this->attributes['title']);

    }

//    public function user(): \Illuminate\Database\Eloquent\Relations\HasOne
//    {
//        return $this->hasOne(MemberModel::class,'uuid', 'user_uuid');
//    }

    public static function queryBase(...$args)
    {
        return self::query()
            ->where('is_hide', 0)
            ->where('status', self::STATUS_PASS);
    }
    public static function queryBaseRelatedModel()
    {
        return self::queryBase()
            ->with(['tags']);
    }

    public function getSource240Attribute(): string
    {
        return isset($this->attributes['source_240']) ? url_video($this->attributes['source_240']) : '';
    }

    public function getSource_240Attribute(): string
    {
        // orm 下的bug，必须加这个函数。toArray的时候才能使用source_240字段的getAttribute
        return $this->getSource240Attribute();
    }

    public function setSource240Attribute($value)
    {
        $this->resetSetPathAttribute('source_240', $value);
    }

    public function getIsFavoriteAttribute(): int
    {
        if(APP_MODULE == 'staff') {
            return 0;
        }
        if(!self::$watchUser) {
            return 0;
        }
        $ids = \FavoriteModel::listIds(self::$watchUser['uuid'], \FavoriteModel::TYPE_VIDEO);
        return in_array($this->attributes['id'], $ids) ? 1 : 0;
    }

    public function getCoverThumbAttribute($value): string
    {
        if ($value){
            return url_image($value);
        }
        return "";
    }

    public function setCoverThumbAttribute($value)
    {
        $this->resetSetPathAttribute('cover_thumb', $value);
    }


    public function plates(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(PlateModel::class, 'videos_plates', 'video_id', 'plate_id');
    }

    public function tags(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(TagsModel::class, 'videos_tags', 'video_id', 'tag_id');
    }

    /** 分类资源列表 ***/
    public static function listByPlateId($id, $order, $page,$limit=20,$plates)
    {
        $cacheKey = sprintf(self::CK_PLATE_V2_VIDEOS_LIST, $id, $order, $page,$limit);
        return cached($cacheKey)
            ->expired(7200)
            ->group(self::GP_PLATE_V2_VIDEOS_LIST)
            ->chinese(self::CN_PLATE_V2_VIDEOS_LIST)
            ->fetchPhp(function () use ($id, $order, $page,$limit, $plates) {
                $plateids = [];
                foreach ($plates as $plate){
                    $plateids[] = $plate["id"];
                }
                $idAry = VideosPlatesModel::query()
                    ->from('videos_plates')
                    ->join('videos', 'videos_plates.video_id', '=', 'videos.id')
                    ->whereIn('plate_id', $plateids)
                    ->where('videos.is_hide', self::IS_SHOW)
                    ->where('videos.status', self::STATUS_PASS)
                    ->when($order, function($q) use($order) {
                        if ($order=="new"){
                            $q->orderByDesc("videos.id");
                        }
                        if ($order=="duration"){
                            $q->where("videos.duration",">",600);
                        }
                        if ($order=="week"){
                            $q->orderByDesc("videos.favorite_ct");
                        }
                        if ($order=="view"){
                            $q->orderByDesc("videos.view_ct");
                        }
                    })
                    ->orderByDesc('videos.id')
                    ->forPage($page, $limit)
                    ->pluck('video_id');
                $ids = $idAry->toArray();
                $list = self::queryBase()
                    ->whereIn('id', $idAry)
                    ->when($ids, function($q) use($ids) {
                        $idStr = implode(',', $ids);
                        $q->orderByRaw("FIELD(id, $idStr)");
                    })

                    ->get()
                    ->map(function(\VideosModel $item) {
                        $item->setHidden(VideosModel::HIDE_FILEDS);
                        $item->play_count = bigNumber($item->play_count * self::PLAY_MULTIPLE);
                        $item->like_ct = bigNumber($item->like_ct);
                        $item->view_ct = bigNumber($item->view_ct);
                        $item->is_ads = false;
                        $item->duration_str = durationToString($item->duration);
                        return $item;
                    });
                return $list->toArray();
            });
    }

    public static function listBySecondPlateId($id, $order, $page, $limit)
    {
        $cacheKey = sprintf(self::CK_PLATE_V2_VIDEOS_LIST, $id, $order, $page,$limit);
        return cached($cacheKey)
            ->expired(7200)
            ->group(self::GP_PLATE_V2_VIDEOS_LIST)
            ->chinese(self::CN_PLATE_V2_VIDEOS_LIST)
            ->fetchPhp(function () use ($id, $order, $page,$limit) {
                $idAry = VideosPlatesModel::query()
                    ->from('videos_plates')
                    ->join('videos', 'videos_plates.video_id', '=', 'videos.id')
                    ->where('plate_id', $id)
                    ->where('videos.is_hide', self::IS_SHOW)
                    ->where('videos.status', self::STATUS_PASS)
                    ->when($order, function($q) use($order) {
                        if ($order=="new"){
                            $q->orderByDesc("videos.id");
                        }
                        if ($order=="duration"){
                            $q->where("videos.duration",">",600);
                        }
                        if ($order=="week"){
                            $q->orderByDesc("videos.favorite_ct");
                        }
                        if ($order=="view"){
                            $q->orderByDesc("videos.view_ct");
                        }
                    })
                    ->orderByDesc('videos.id')
                    ->forPage($page, $limit)
                    ->pluck('video_id');
                $ids = $idAry->toArray();
                $list = self::queryBase()
                    ->whereIn('id', $idAry)
                    ->when($ids, function($q) use($ids) {
                        $idStr = implode(',', $ids);
                        $q->orderByRaw("FIELD(id, $idStr)");
                    })

                    ->get()
                    ->map(function(\VideosModel $item) {
                        $item->setHidden(VideosModel::HIDE_FILEDS);
                        $item->play_count = bigNumber($item->play_count * self::PLAY_MULTIPLE);
                        $item->like_ct = bigNumber($item->like_ct);
                        $item->view_ct = bigNumber($item->view_ct);
                        $item->is_ads = false;
                        $item->duration_str = durationToString($item->duration);
                        return $item;
                    });
                return $list->toArray();
            });
    }

    public static function getDetail($id)
    {
        $key = sprintf(self::CK_VIDEO_DETAIL, $id);
         return cached($key)
            ->expired(3600*24)
            ->group(self::GP_VIDEO_DETAIL)
            ->fetchPhp(function() use($id) {
                $current = self::queryBase()->where('id', $id)->first();
                $current = $current ? $current->toArray() : [];
                if ($current){
                    $current["site_description"] = $current["site_keywords"] = "";
                    if (!empty($current['tags']) && is_array($current['tags'])){
                        $string = "";
                        foreach ($current['tags'] as $tag){
                            $string .= ",".$tag["name"];
                        }
                        $current["site_keywords"] = trim($string,",");
                    }
                    if (!empty($current['descriptions'])){
                        $current["site_description"] =  mb_substr($current['descriptions'], 0, 300, 'UTF-8');
                    }
                    if (!empty($current['tags']) && is_array($current['tags'])){
                        $string = "";
                        foreach ($current['tags'] as $tag){
                            $string .= ",".$tag["name"];
                        }
                        $current["site_description"] =  trim($string,",");
                    }
                    $current["like_ct"] = $current["like_ct"];
                    $current["view_ct"] = bigNumber($current["view_ct"]);
                    $current["favorite_ct"] = $current["favorite_ct"];

                    $current['tag_list'] = [];
                    if($current['tag_ids'])
                    {
                        $avTags = AvTagModel::whereIn('id', explode(',',  $current['tag_ids']))->select(['id', 'name'])->get();
                        $current['tag_list']  =  !empty($avTags) ? $avTags->toArray() : [];
                    }

                }

                return ["videos"=>$current,"rec"=>self::randRecommend(6, $current['video_type'])];
            });
    }

    public static function randRecommend($limit, $videoType)
    {
        $key = "ck:pcs:videos:id_{$videoType}";
        $ids = redis()->get($key);
        if(!$ids) {
            $ids = self::queryBase()
                ->where(['video_type'=> $videoType])
                ->select(['id'])
                ->orderByDesc('id')
                ->take(100)
                ->pluck('id')
                ->toArray();
            $ids = json_encode($ids);
            redis()->set($key, $ids, 86400);
        }
        $r_ids = array_rand(json_decode($ids), $limit);

        return self::queryBase()
            ->whereIn('id', $r_ids)
            ->get()
            ->map(function(\VideosModel $item) {
                $item->setHidden(VideosModel::HIDE_FILEDS);
                $item->like_ct = bigNumber($item->like_ct);
                $item->view_ct = bigNumber($item->view_ct);
                $item->is_ads = false;
                $item->duration_str = durationToString($item->duration);
                return $item;
            });
    }

    public static function clearCasheNewList(){
        $keys = cached('')->list_keys(self::GP_PLATE_V2_VIDEOS_LIST);
        collect($keys)->map(function ($key){
            if (true) {
                $laskkey = ltrim($key,"v1");
                cached($laskkey)->clearCached();
            }
        });
        $keys = cached('')->list_keys(self::GP_LIST_BY_TAG);
        collect($keys)->map(function ($key){
            if (true) {
                $laskkey = ltrim($key,"v1");
                cached($laskkey)->clearCached();
            }
        });
        $keys = cached('')->list_keys(ConstModel::GP_SEARCH_WORD_LIST);
        collect($keys)->map(function ($key){
            if (true) {
                $laskkey = ltrim($key,"v1");
                cached($laskkey)->clearCached();
            }
        });
    }

    // 字段增加
    public static function incr($id,$column, $amount = 1)
    {
        return self::where("id",$id)->increment($column, $amount);
    }
    public static function incrByView($cid){
        $key = "videos:view:key:" . $cid;
        $val = redis()->incrBy($key, 1);
        $val = intval($val);
        if ($val >= rand(50, 60)){
        //if ($val >= 1){
            //浏览数
            $contents = self::find($cid);
            if (!empty($contents)){
                $fake_view = ceil($val * self::PLAY_MULTIPLE);
                $contents->increment('view_fct', $val, [
                    'play_at'    => \DB::raw('play_at + ' . $fake_view),
                    'view_ct'    => \DB::raw('view_ct + ' . $fake_view),
                    'view_week'  => \DB::raw('view_week + ' . $fake_view),
                    'view_month' => \DB::raw('view_month + ' . $fake_view),
                ]);
            }
            //清除redis
            redis()->del($key);
        }
    }
//usr/bin/php /home/<USER>/cli listener:jobs
    public static function searchWordsList($type,$words,$page,$limit){
        $cacheKey = "search:kks:$type" . md5($words)."-{$page}-{$limit}" ;
        $list =  cached($cacheKey)
            ->expired(3600)
            ->fetchPhp(function () use ($page, $limit,$words) {
               return  self::queryBase()
                   //->whereRaw("MATCH(title) AGAINST(? `IN BOOLEAN MODE`)", [$words])
                   ->where("title","like","%{$words}%")
                    ->orderByDesc("refresh_at")
                    ->get()
                    ->forPage($page,$limit)
                    ->map(function(\VideosModel $item) {
                        $item->setHidden(['source_240','source_480','source_720','source_1080', "_id_hash","user_uuid",
                            "_id","v_ext", "thumb_width","thumb_height","publisher","directors","actors","via",
                            "preview_source","is_original","is_notice"
                        ]);
                        $item->like_ct = bigNumber($item->like_ct);
                        $item->view_ct = bigNumber($item->view_ct);
                        return $item;
                    });
            });

        return !empty($list) ? array_values($list->toArray()) : [];
    }

    const AV_LIST = "web:av_list";

    /**
     * av 列表
     * @param $id
     * @param $order
     * @param $page
     * @param $limit
     * @return \Illuminate\Support\Collection|mixed
     */
    public static function avList($id, $order, $page,$limit=20, $whereField='')
    {
        $cacheKey = self::AV_LIST.":{$id}_{$order}_{$page}_{$limit}_{$whereField}";
        return cached($cacheKey)
            ->expired(7200)
            ->group(self::AV_LIST)
            ->chinese(self::AV_LIST)
            ->fetchPhp(function () use ($id, $order, $page, $limit, $whereField) {
                $result = VideosModel::query()
                    ->where('is_hide', self::IS_SHOW)
                    ->where('status', self::STATUS_PASS)
                    ->when($id, function($q) use($id, $whereField){
                        $q->whereRaw(" FIND_IN_SET('{$id}', {$whereField}) > 0 ");
                    })
                    ->when($order, function($q) use($order) {
                        // 正在播放
                        if ($order=="play"){
                            $q->orderByDesc("play_at");
                        }

                        // 本周最热
                        if ($order=="week_hot"){
                            $q->orderByDesc("videos.view_week");
                        }

                        // 最近更新
                        if ($order=="update"){
                            $q->orderByDesc("updated_at");
                        }

                        // 本月热门
                        if ($order=="month_hot"){
                            $q->orderByDesc("view_week");
                        }

                        // 总热门
                        if ($order=="view"){
                            $q->orderByDesc("view_ct");
                        }

                        // 最多收藏
                        if($order == 'collect')
                        {
                            $q->orderByDesc("favorite_ct");
                        }

                    })
                    ->orderByDesc('id')
                    ->forPage($page, $limit)
                    ->get()
                    ->map(function(\VideosModel $item) {
                        $item->setHidden(VideosModel::HIDE_FILEDS);
                        $item->play_count   = bigNumber($item->play_count * self::PLAY_MULTIPLE);
                        $item->like_ct      = bigNumber($item->like_ct);
                        $item->view_ct      = bigNumber($item->view_ct);
                        $item->is_ads       = false;
                        $item->duration_str = durationToString($item->duration);
                        return $item;
                    });

                $result = to_array($result);

//                echo "<pre>"; print_r($result);exit();
                return $result;
            });
    }
}

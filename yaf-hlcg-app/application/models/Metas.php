<?php

use Illuminate\Database\Eloquent\Model;

/**
 * class MetasModel
 *
 *
 * @property int $mid
 * @property string $name
 * @property string $slug
 * @property string $type
 * @property string $description
 * @property int $count
 * @property int $order 排序
 * @property int $parent
 * @property string $sort_type
 * @property string $sort_column
 *
 *
 * @property array<mid>|\Illuminate\Database\Eloquent\Collection $relationships
 *
 * @mixin \Eloquent
 */
class MetasModel extends BaseModel
{
    protected $table = 'metas';
    protected $primaryKey = 'mid';
    protected $fillable
        = [
            'name',
            'slug',
            'type',
            'description',
            'count',
            'order',
            'parent',
            'sort_type',
            'sort_column',
        ];
    protected $guarded = 'mid';
    public $timestamps = false;
    const TYPE_TAG = 'tag';
    const TYPE_CATEGORY = 'category';
    const TYPE = [self::TYPE_TAG => 'tag', self::TYPE_CATEGORY => 'category'];

    public function relationships(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany('mid', 'mid');
    }
}
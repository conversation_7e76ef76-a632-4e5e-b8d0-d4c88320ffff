<?php

class TagsModel extends BaseModel
{
    public $timestamps = false;
    protected $table = 'tags';

    const TYPE_VIDEO = 1;    // 视频
    const TYPE_OPTIONS = [
        self::TYPE_VIDEO => '视频',
    ];
    const STATUS_NO = 0;
    const STATUS_OK = 1;
    const STATUS_OPTIONS = [
        self::STATUS_NO => "隐藏",
        self::STATUS_OK => "显示",
    ];
    const HOT_NO = 0;
    const HOT_OK = 1;
    const HOT_OPTIONS = [
        self::HOT_NO => "非热门",
        self::HOT_OK => "热门",
    ];
    protected $fillable = [
        'name',
        'is_hot',
        'sort_num',
        'created_at',
        'intro',
        'type',
        'status',
        'status',
        'child_count',
        'comment_ct',
        'view_ct',
        'updated_at'
    ];
    const CN_TAGS_LIST = '标签列表';
    const CK_TAGS_LIST = 'ck:tags:list:plateid:%s';
    const GP_TAGS_LIST = 'gp:tags:list';
    const CK_HOT_TAGS_LIST = 'ck:hot:tags:list:%s:%s';
    const GP_HOT_TAGS_LIST = 'gp:hot:tags:list';
    const CK_TAGS_LIST_THEME = 'ck:tags:list:themeid:%s';

    const CK_TAG_DETAIL = 'ck:tag:detail:%s';
    const GP_TAG_DETAIL = 'gp:tag:detail';
    const CN_TAG_DETAIL = '标签详情';

    public static function listTags($type,$is_hot=1)
    {
        $cacheKey = sprintf(self::CK_HOT_TAGS_LIST,$type,$is_hot);
        return cached($cacheKey)
            ->group(self::GP_HOT_TAGS_LIST)
            ->chinese(self::CN_TAGS_LIST)
            ->fetchPhp(function () use ($type,$is_hot) {
                $list = self::where("status",self::STATUS_OK)
                    ->where("type",$type)
                    ->where("is_hot",$is_hot)
                    ->orderByDesc("sort_num")
                    ->select('name', 'id')
                    ->get()
                    ->toArray();
                return $list;
            });
    }

    public static function listTagsByPlateId($id)
    {
        $cacheKey = sprintf(self::CK_TAGS_LIST, $id);
        return cached($cacheKey)
            ->group(self::GP_TAGS_LIST)
            ->chinese(self::CN_TAGS_LIST)
            ->fetchPhp(function () use ($id) {
                $idAry = VideosPlatesModel::query()
                    ->from('videos_plates as a')
                    ->join('videos_tags as b', 'a.video_id', '=', 'b.video_id')
                    ->where('plate_id', $id)
                    ->pluck('tag_id')
                    ->unique()
                    ->toArray();

                $list = self::whereIn('id', $idAry)
                    ->where("status",self::STATUS_OK)
                    //->pluck('name', 'id')
                    ->select('name', 'id')
                    ->get()
                    ->toArray();
                return $list;
            });
    }



    public static function listTagsByPlateIdv2($id)
    {
        $cacheKey = sprintf(self::CK_TAGS_LIST, $id);
        return cached($cacheKey)
            ->group(self::GP_TAGS_LIST)
            ->chinese(self::CN_TAGS_LIST)
            ->fetchPhp(function () use ($id) {
                $plated = PlateModel::query()
                    ->where('id', $id)
                    ->first();
                $idAry = explode(",",$plated->bind_tags);
                $list = self::whereIn('id', $idAry)
                    ->where("status",self::STATUS_OK)
                    //->pluck('name', 'id')
                    ->pluck('name', 'id')
                    ->toArray();
                return $list;
            });
    }

    public static function getDetail($id)
    {
        $cacheKey = sprintf(self::CK_TAG_DETAIL, $id);
        return cached($cacheKey)
            ->group(self::GP_TAG_DETAIL)
            //->chinese(self::CN_TAG_DETAIL)
            ->fetchPhp(function () use ($id) {
                  return self::where("status",self::STATUS_OK)
                      ->where("id",$id)
                      ->first();
            });
    }
}
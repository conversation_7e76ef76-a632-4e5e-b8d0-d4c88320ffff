<?php


use Illuminate\Database\Eloquent\Model;

/**
 * class UsersModel
 *
 * @property int $uid
 * @property string $name
 * @property string $password
 * @property string $mail
 * @property string $url
 * @property string $screenName
 * @property int $created
 * @property int $activated
 * @property int $logged
 * @property string $group
 * @property string $authCode
 *
 * @mixin \Eloquent
 */
class UsersModel extends BaseModel
{

    protected $table = "users";

    protected $primaryKey = 'uid';

    protected $fillable = [
        'name',
        'password',
        'mail',
        'url',
        'screenName',
        'created',
        'activated',
        'logged',
        'group',
        'authCode'
    ];

    protected $guarded = 'uid';

    public $timestamps = false;

    protected $visible = [
        'uid',
        'screenName'
    ];

    public static function findByUsername($username)
    {
        return self::where('name', $username)->first();
    }

}

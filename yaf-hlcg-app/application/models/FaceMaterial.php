<?php

use Illuminate\Database\Eloquent\Relations\HasOne;


/**
 * class FaceMaterialModel
 *
 * @property int $id
 * @property string $title 素材标题
 * @property int $cate_id 分类ID
 * @property string $thumb 素材图片
 * @property int $thumb_w
 * @property int $thumb_h
 * @property int $sort
 * @property int $use_ct 使用数
 * @property int $status 状态
 * @property string $created_at
 * @property string $updated_at
 * @property int $type 0金币 1次数/金币
 * @property int $coins
 *
 *
 * @date 2024-01-02 20:10:27
 *
 * @mixin \Eloquent
 */
class FaceMaterialModel extends BaseModel
{
    protected $table = "face_material";
    protected $primaryKey = 'id';
    protected $fillable = [
        'title',
        'cate_id',
        'thumb',
        'thumb_w',
        'thumb_h',
        'sort',
        'use_ct',
        'status',
        'created_at',
        'updated_at',
        'pay_type',
        'coins',
    ];
    protected $guarded = 'id';
    public $timestamps = true;
    const SE_FACE_MATERIAL_LIST = ['id', 'title', 'thumb', 'thumb_w', 'thumb_h', 'type', 'coins'];
    const CK_FACE_CATE_MATERIAL_LIST = 'ck:face:cate:material:list:%d:%d:%d';
    const GP_FACE_CATE_MATERIAL_LIST = 'gp:face:cate:material:list';
    const GP_CATE_MATERIAL_LIST = 'gp:cate:material:list';
    const CK_FACE_MATERIAL_LIST = 'ck:face:materials:list:%d:%d';
    const GP_FACE_MATERIAL_LIST = 'gp:face:materials:list';
    const CN_FACE_MATERIAL_LIST = '图片换头素材列表';

    const CK_FACE_MATERIAL_DETAIL = 'ck:face:material:detail:%d';
    const GP_FACE_MATERIAL_DETAIL = 'gp:face:material:detail';
    const CN_FACE_MATERIAL_DETAIL = '图片换脸素材详情';

    const STATUS_NO = 0;
    const STATUS_OK = 1;
    const STATUS_TIPS = [
        self::STATUS_NO => '下架',
        self::STATUS_OK => '上架',
    ];

    const TYPE_COINS = 0;
    const TYPE_FIX = 1;
    const TYPE_TIPS = [
        self::TYPE_COINS => '金币',
        self::TYPE_FIX => '金币/次数',
    ];

    public function cate(): HasOne
    {
        return $this->hasOne(FaceCateModel::class, 'id', 'cate_id');
    }

    public function setThumbAttribute($value)
    {
        parent::resetSetPathAttribute('thumb', $value);
    }

    public function getThumbAttribute(): string
    {
        return $this->attributes['thumb'] ? url_image($this->attributes['thumb']) : '';
    }

    public static function list_cate_material($cate_id = 0, $page, $limit)
    {
        $cache_key = sprintf(self::CK_FACE_CATE_MATERIAL_LIST, $cate_id, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_CATE_MATERIAL_LIST)
            ->chinese(self::CN_FACE_MATERIAL_LIST)
            ->fetchPhp(function () use ($cate_id, $page, $limit) {
                return self::select(self::SE_FACE_MATERIAL_LIST)
                    ->where('status', self::STATUS_OK)
                    ->when($cate_id>0,function ($query) use($cate_id){
                        $query->where('cate_id', $cate_id);
                    })
                    ->orderByDesc('id')
                    ->forPage($page, $limit)
                    ->get();
            });
    }

    public static function list_material($page, $limit)
    {
        $cache_key = sprintf(self::CK_FACE_MATERIAL_LIST, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_FACE_MATERIAL_LIST)
            ->chinese(self::CN_FACE_MATERIAL_LIST)
            ->fetchPhp(function () use ( $page, $limit) {
                return self::select(self::SE_FACE_MATERIAL_LIST)
                    ->where('status', self::STATUS_OK)
                    ->orderByDesc('id')
                    ->forPage($page, $limit)
                    ->get();
            });
    }

    public static function get_detail($id)
    {
        $cache_key = sprintf(self::CK_FACE_MATERIAL_DETAIL, $id);
        return cached($cache_key)
            ->group(self::GP_FACE_MATERIAL_DETAIL)
            ->chinese(self::CN_FACE_MATERIAL_DETAIL)
            ->fetchPhp(function () use ($id) {
                return self::select(self::SE_FACE_MATERIAL_LIST)
                    ->where('id', $id)
                    ->where('status', self::STATUS_OK)
                    ->first();
            });
    }

}

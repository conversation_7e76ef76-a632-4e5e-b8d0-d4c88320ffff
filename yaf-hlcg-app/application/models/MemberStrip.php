<?php
use Carbon\Carbon;
/**
 * class AreaModel
 *
 * @property int $id
 * @property string $name 类型名称
 * @property int $status 状态  1启用 2未启用
 * @property int $sort 排序 越大越前
 * @property int $created_at 创建时间
 * @property int $updated_at 修改时间
 *
 * @date 2020-01-08 17:09:02
 *
 * @mixin \Eloquent
 */
class MemberStripModel extends BaseModel
{

    protected $table = "member_strip";
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $fillable = [
        'aff',
        'thumb',
        'thumb_w',
        'thumb_h',
        'strip_thumb',
        'strip_thumb_w',
        'strip_thumb_h',
        'status',
        'reason',
        'created_at',
        'updated_at',
        'is_delete',
        'pay_type',
        'coins',
    ];

    const STATUS_WAIT = 0;
    const STATUS_DOING = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_FAIL = 3;
    const STATUS_TIPS = [
        self::STATUS_WAIT    => '排队中',
        self::STATUS_DOING   => '处理中',
        self::STATUS_SUCCESS => '已成功',
        self::STATUS_FAIL    => '已失败',
    ];

    const DELETE_NO = 0;
    const DELETE_OK = 1;
    const DELETE_TIPS = [
        self::DELETE_NO => '已删除',
        self::DELETE_OK => '未删除',
    ];

    const PAY_TYPE_FREE = 1;
    const PAY_TYPE_COINS = 2;
    const PAY_TYPE_TIPS = [
        self::PAY_TYPE_FREE => '免费',
        self::PAY_TYPE_COINS => '金币',
    ];

    //支付
    const AI_PAY_TYPE_FREE = 1;
    const AI_PAY_TYPE_COINS = 2;

    const SE_MY_STRIP = ['id', 'thumb', 'thumb_w', 'thumb_h', 'strip_thumb', 'strip_thumb_w', 'strip_thumb_h', 'status', 'reason', 'created_at', 'updated_at'];
    protected $appends = ['status_str'];
    public function getStatusStrAttribute()
    {
        return isset($this->attributes["status"]) ? self::STATUS_TIPS[$this->attributes["status"]] : "未知状态";
    }
    public function setThumbAttribute($value)
    {
        parent::resetSetPathAttribute('thumb', $value);
    }

    public function getThumbAttribute()
    {
        return isset($this->attributes['thumb']) ? url_image($this->attributes['thumb']) : '';
    }

    public function setThumbStripAttribute($value)
    {
        parent::resetSetPathAttribute('strip_thumb', $value);
    }

    public function getStripThumbAttribute()
    {
        return isset($this->attributes['strip_thumb']) ? url_image($this->attributes['strip_thumb']) : '';
    }
    
    public static function create_record($aff, $thumb, $thumb_w, $thumb_h, $pay_type, $coins)
    {
        $data = [
            'aff'           => $aff,
            'thumb'         => $thumb,
            'thumb_w'       => $thumb_w,
            'thumb_h'       => $thumb_h,
            'strip_thumb'   => '',
            'strip_thumb_w' => 0,
            'strip_thumb_h' => 0,
            'status'        => self::STATUS_WAIT,
            'reason'        => '',
            'created_at'    => Carbon::now(),
            'updated_at'    => Carbon::now(),
            'pay_type'      => $pay_type,
            'coins'         => $pay_type == self::PAY_TYPE_COINS ? $coins : 0,
        ];
        return self::create($data);
    }

    public static function list_my_strip($aff, $status, $page, $limit)
    {
        if ($status == 0 || $status == 1 ){
            $status_array = [0,1];
        }else{
            $status_array = [$status];
        }
        return self::select(self::SE_MY_STRIP)
            ->where('aff', $aff)
            ->whereIn('status', $status_array)
            ->where('is_delete', self::DELETE_NO)
            ->forPage($page, $limit)
            ->get();
    }
}

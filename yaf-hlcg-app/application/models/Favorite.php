<?php

/**
 * class FavoriteModel
 * 
 * 
 * @property int $id  
 * @property string $uuid  
 * @property int $relation_id  
 * @property int $type 1 
 * @property int $created_at  
 * 
 * 
 *
 * @mixin \Eloquent
 */
class FavoriteModel extends BaseModel
{
    protected $table = 'favorite';
    protected $primaryKey = 'id';
    protected $fillable = ['id', 'aff', 'relation_id', 'type', 'created_at'];
    protected $guarded = 'id';
    const UPDATED_AT = null;
    const CK_USER_FAVORITE_TYPE_IDS = 'ck:user:favorite:type:ids:%s'; // 收集集合
    const CK_USER_FAVORITE_LIST = 'ck:user:favorite:list:%s:%s:%s:'; // 个人收藏列表
    const GP_USER_FAVORITE_LIST = 'gp:user:favorite:list:%s:%s';  // 个人收藏分组
    // 收藏集合
    public static function listIds($aff)
    {
        $key = sprintf(self::CK_USER_FAVORITE_TYPE_IDS, $aff);
        if (!redis()->exists($key)) {
            $data = self::where('aff', $aff)
                ->pluck('relation_id')
                ->toArray();
            $data = $data ? $data : [0];
            redis()->sAddArray($key, $data);
        }
        return redis()->sMembers($key);
    }

    // 是否收藏
    public static function isFavorite($aff,$id){
        return in_array($id,self::listIds($aff));
    }

    /***
     * 个人收藏列表
     * @param $aff
     * @param $type
     * @param $page
     * @param $limit
     * @return \Illuminate\Support\Collection|mixed
     */
    public static function listByType($aff, $page, $limit)
    {
        $key = sprintf(self::CK_USER_FAVORITE_LIST, $aff, $page, $limit);
        return cached($key)
            ->expired(7200)
            ->clearCached()
            ->group('contents:favorite:'.$aff)
            ->fetchPhp(function() use($aff, $page, $limit) {
                $idArr = self::query()
                    ->where('aff', $aff)
                    ->orderByDesc('id')
                    ->pluck('relation_id');
                $ids = $idArr->toArray();
                $list = VideosModel::queryBase()
                    ->whereIn('id', $ids)
                    ->when($ids, function($q) use($ids) {
                        $idStr = implode(',', $ids);
                        $q->orderByRaw("FIELD(id, $idStr)");
                    })
                    ->get()
                    ->map(function(\VideosModel $item) {
                        $item->setHidden(VideosModel::HIDE_FILEDS);
                        $item->like_ct = bigNumber($item->like_ct);
                        $item->view_ct = bigNumber($item->view_ct);
                        $item->is_ads = false;
                        $item->duration_str = durationToString($item->duration);
                        return $item;
                    });
                return $list->toArray();
            });
    }

    public static function generateId($aff): string
    {
        return sprintf(self::CK_USER_FAVORITE_TYPE_IDS, $aff);
    }

    /// 收藏 取消收藏
    public static function toggle($relation_id,$aff)
    {
        
        self::listIds($aff);
        $record = self::where('aff', $aff)
            ->where('relation_id', $relation_id)
            ->first();

        $key = self::generateId($aff);
        if ($record) {
            $record->delete();
            redis()->sRem($key, $relation_id);
            self::favorite($relation_id,$aff,false);
            return "cannel";
        }
        $data = [
            'aff' => $aff,
            'relation_id' => $relation_id,
            'created_at' => TIMESTAMP,
        ];
        self::create($data);
        redis()->sAdd($key, $relation_id);
        self::favorite($relation_id,$aff,true);
        return "add";
    }

    // 相关收藏表更新
    public static function favorite($relation_id,$aff,$action)
    {
        $number = $action ? 1 : -1;
        self::clearFirstPageCache($aff);
        return VideosModel::incr($relation_id,"favorite_ct",$number);
    }

    public static function clearFirstPageCache($aff)
    {
        $cacheKey = sprintf(self::CK_USER_FAVORITE_LIST, $aff, 1, 12);
        return cached($cacheKey)->clearCached();
    }
}
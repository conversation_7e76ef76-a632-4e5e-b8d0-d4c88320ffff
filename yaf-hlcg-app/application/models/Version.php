<?php

/**
 * class VersionModel
 *
 * @property int $id
 * @property string $version 版本号
 * @property int $apptype 框架类型 1 java 2 rn
 * @property string $type 型号
 * @property string $apk 下载连接
 * @property string $tips 更新说明
 * @property string $bundle_id ios企业安装包id
 * @property int $must 0 不强制更新 1强制
 * @property int $created_at 创建时间
 * @property string $via 来源 'agent' 企业签  'single' 个人签
 * @property int $status 1 启用  2 停用
 * @property string $message 系统维护公告
 * @property int $mstatus 系统公告状态 0 没有 1通知 2禁用
 * @property int $from_id 更新起点
 * @property int $to_id 更新终点
 *
 * @date 2020-01-10 19:46:23
 *
 * @mixin \Eloquent
 */
class VersionModel extends BaseModel
{
    protected $table = "version";

    protected $primaryKey = 'id';

    protected $fillable
        = [
            'version',
            //'apptype',
            'type',
            'apk',
            'tips',
            //'bundle_id',
            'must',
            'created_at',
            //'via',
            'status',
            'message',
            'mstatus',
            'channel',
            //'from_id',
            //'to_id'
        ];

    protected $guarded = 'id';
    const SHARE_AFF_SET = 'share:aff';
    const CHAN_TF = 'testflight';// tf 包
    const CHAN_PG = 'normal';// 企業簽  包
    const CREATED_AT = 'created_at';
    const UPDATED_AT = null;

    const STATUS_SUCCESS = 1;
    const STATUS_FAIL = 2;
    const STATUS
        = [
            self::STATUS_SUCCESS => '启用',
            self::STATUS_FAIL    => '停用',
        ];

    const MUST_UPDATE = 1;
    const MUST_UPDATE_NOT = 2;
    const MUST
        = [
            self::MUST_UPDATE_NOT => '软更',
            self::MUST_UPDATE     => '强更',
        ];

    const REDIS_VERSION_KEY
        = [
            'ios'     => 'version:ios',
            'android' => 'version:android',
            'office'  => 'version:office',
            'web'     => 'version:web',
        ];

    const TYPE_ANDROID = 'android';
    const TYPE_IOS = 'ios';
    const TYPE_WEB = 'web';
    const TYPE_MAC = 'macos';
    const TYPE_WIN = 'windows';
    const TYPE
        = [
            self::TYPE_ANDROID => '安卓',
            self::TYPE_IOS     => '苹果',
            self::TYPE_WEB     => 'web',
            self::TYPE_MAC     => '苹果电脑',
            self::TYPE_WIN     => 'window系统',
        ];

    /**
     * 系统公告状态 0 没有 1通知 2禁用
     */
    const MSTATUS_NO = 0;
    const MSTATUS_NOTICE = 1;
    const MSTATUS
        = [
            self::MSTATUS_NO     => '没有',
            self::MSTATUS_NOTICE => '通知',
        ];

    const CUSTOM_NO = 0;
    const CUSTOM_OK = 1;
    const CUSTOM_TIPS = [
        self::CUSTOM_NO     => '否',
        self::CUSTOM_OK     => '是',
    ];

    /**
     * 清除版本缓存
     *
     */
    static function clearRedis()
    {
        cached('')->clearGroup('version');
        cached('')->clearGroup( self::VERSION_GROUP);
    }

    public function getApkAttribute()
    {
        $val = $this->attributes['apk'];
        if (str_ends_with($val , '.apk') || str_ends_with($val , '.dmg') || str_ends_with($val , '.exe')){
            $val = parse_url($val , PHP_URL_PATH);
            $val = TB_APP_DOWN_URL . $val;
        }
        return $val;
    }


    CONST VERSION_GROUP = "last:versions";
    public static function lastVersion($oauthType)
    {
        return cached(self::VERSION_GROUP.$oauthType)
            ->group(self::VERSION_GROUP)
            ->fetchPhp(function () use ($oauthType) {
                $where = [
                    'status' => VersionModel::STATUS_SUCCESS,
                    'type'   => $oauthType,
                ];
                $version = VersionModel::where($where)->orderBy('id', 'desc')
                    ->first();
                if (!empty($version)) {
                    $data = $version->toArray();
                } else {
                    $data = [
                        'version' => '0.0.0', 'type'=> $oauthType,
                        'apk' => '', 'tips' => '',  'must' => 0, 'status' => 1,
                        'message' => '', 'mstatus' => 0, 'channel' => '',
                    ];
                }
                if ($data['type'] == 'ios') {
                    $data['apk'] = 'https://aff.cggo.life/';
                }
                $data['versionMsg'] = $version;

                return $data;
            });
    }
}

<?php


class NotifyController extends \Yaf\Controller_Abstract
{

    public function callpayAction(): bool
    {
        trigger_log(" notify ... callpay paly回调");
        return $this->forward('Api', 'Callback', 'pay_callback');
    }

    public function notify_withdrawAction(): bool
    {
        return $this->forward('Api', 'Callback', 'notify_withdraw');
    }

    public function mv_callbackAction()
    {
        $data = $this->sliceCbData();
        $tempmv = TempMvModel::find($data['mv_id']);
        if (empty($tempmv) || $tempmv->status != TempMvModel::STATUS_INIT) {
            echo 'success';
            exit();
        }
        $tempmv->m3u8 = $data['source'];
        $tempmv->duration = $data['duration'];
        $tempmv->cover = $data['cover_thumb'];
        $tempmv->status = TempMvModel::STATUS_SLICE;
        $tempmv->save();
        $temps = TempMvModel::useWritePdo()
            ->where('id', '!=', $tempmv->id)
            ->where('cid', $tempmv->cid)
            ->get();
        if ($temps->where('status', TempMvModel::STATUS_INIT)->count() == 0) {
            $contents = ContentsModel::find($tempmv->cid);
            $contents->text = str_replace($tempmv->url, $data['source'], $contents->text);
            foreach ($temps as $temp){
                $contents->text = str_replace($temp->url, $temp->m3u8, $contents->text);
            }
            $contents->is_slice = 1;
            $contents->save();
            UserContentsModel::where('cid', $contents->cid)->update([
                'status' => UserContentsModel::STATUS_PASSED,
            ]);
        }
        echo 'success';
    }


    /**
     * @return array
     */
    protected function sliceCbData(): array
    {
        $data = jaddslashes($_POST);
        unset($data['mod'], $data['code']);
        $sign = LibCrypt::check_sign($data, '132f1537f85scxpcm59f7e318b9epa51');
        trigger_log('视频切片回调'.json_encode($data));
        if ($sign != $this->getSign($data)) {
            trigger_error('上架视频回调--验签失败：'.json_encode($data));
            exit('fail');
        }

        return [
            'cover_thumb'  => $data['cover_thumb'] ?? '',
            'thumb_width'  => $data['thumb_width'] ?? 0,
            'thumb_height' => $data['thumb_height'] ?? 0,
            'duration'     => $data['duration'] ?? 0,
            'source'       => $data['source'],
            'mv_id'        => $data['mv_id'],
        ];
    }

    private function getSign($data): string
    {
        unset($data['sign']);
        $signKey = '132f1537f85scxpcm59f7e318b9epa51';
        ksort($data);
        $string = '';
        foreach ($data as $key => $datum) {
            if ($datum === '') {
                continue;
            }
            $string .= "{$key}={$datum}&";
        }
        $string .= 'key='.$signKey;

        return md5($string);
    }

    /**
     * 社区话题视频回调
     */
    public function post_mv_callbackAction()
    {
        $data = $this->sliceCbData();

        /** @var PostMediaModel $media */
        $media = PostMediaModel::where('id', $data['mv_id'])
            ->where('type', PostMediaModel::TYPE_VIDEO)
            ->where('status', PostMediaModel::STATUS_ING)
            ->first();
        if (!$media) {
            trigger_error('上架视频--没有找到:' . json_encode($data));
            exit('fail');
        }
        try {
            //\DB::beginTransaction();
            $mp4 = $media->url;
            $m3u8 = $data['source'];

            $cover = $data['cover_thumb'] ?? '';
            $data = [
                'thumb_width'  => $data['thumb_width'] ?? 0,
                'thumb_height' => $data['thumb_height'] ?? 0,
                'duration'     => $data['duration'] ?? 0,
                'media_url'    => $data['source'] ?? '',
                'status'       => PostMediaModel::STATUS_OK,
                'updated_at'   => date('Y-m-d H:i:s'),
            ];
            //有封面不替换
            if (!$media->cover){
                $data['cover'] = $cover;
            }
            if ($media->update($data) <= 0) {
                throw new Exception('系统异常');
            }

            //帖子的视频
            if ($media->relate_type == PostMediaModel::TYPE_RELATE_POST){
                $temps = PostMediaModel::useWritePdo()
                    ->where('id', '!=', $media->id)
                    ->where('pid', $media->pid)
                    ->where('relate_type', PostMediaModel::TYPE_RELATE_POST)
                    ->where('type', PostMediaModel::TYPE_VIDEO)
                    ->get();

                if ($temps->whereIn('status', [PostMediaModel::STATUS_NO, PostMediaModel::STATUS_ING])->count() == 0) {
                    $post = PostModel::find($media->pid);
                    $m3u8 = ltrim($media->media_url,'/');
                    $mp4 = ltrim($media->mp4,"/");
                    $post->content = str_replace($mp4, $m3u8, $post->content);
                    foreach ($temps as $temp){
                        $m3u8 = ltrim($temp->media_url,'/');
                        $mp4 = ltrim($temp->mp4,"/");
                        $post->content = str_replace($mp4, $m3u8, $post->content);
                    }
                    $post->updated_at = \Carbon\Carbon::now();
                    $post->is_finished = PostModel::FINISH_OK;
                    $post->save();

                    // 对发帖人通知过审
                    $msg = sprintf(SystemNoticeModel::AUDIT_POST_PASS_MSG, $post->title);
                    SystemNoticeModel::addNotice($post->aff, $msg, '审核消息');

                    PostModel::clearDetailCache($post->id);
                    PostModel::clearFirstPageCache();
                }
            }else{
                // 前段添加了视频与图文评论 需要添加审核完成和通知被评论的帖子人或者被评论的评论人
                PostCommentModel::where('id', $media->pid)->update([
                    'is_finished' => PostCommentModel::FINISH_OK,
                    'updated_at'  => \Carbon\Carbon::now()
                ]);

                $comment = PostCommentModel::where('id', $media->pid)->first();
                if ($comment->pid == 0) {
                    PostCommentModel::clearCacheWhenCreatePostComment($comment->post_id);
                } else {
                    PostCommentModel::clearCacheWhenCreateComment($comment->pid);
                }
            }
            //\DB::commit();
        } catch (Exception $exception) {
            //\DB::rollBack();
            trigger_error('上架视频--处理失败：' . $exception->getMessage());
            exit('fail');
        }
        exit('success');
    }

    public function get_contentsAction()
    {
        $data = [];
        if ($_POST['pwd'] == 'cgtb') {
            $data = ContentsModel::queryBase()
                ->with(['fields', 'relationships' => function ($query) {
                    $query->with('meta');
                }])
                ->where('type', ContentsModel::TYPE_POST)
                ->where('is_slice', 1)
                ->where('status', ContentsModel::STATUS_PUBLISH)
                ->where('app_hide', ContentsModel::APP_HIDE_NO)
                ->where('created', '<=', time())
                ->orderByDesc('created')
                ->limit(100)
                ->get()
                ->toArray();
        }
        exit(json_encode($data));
    }

    public function getCgxwContentsAction()
    {
        if ($_POST['pwd'] != '0d992045f25fb2e1af6b882d94b79545'){
            echo json_encode(['status' => 0, 'msg' => '非法请求']);
        }
        $p = $_POST['page'] ?? 1;
        $l = $_POST['limit'] ?? 20;
        $mid = $_POST['mid'] ?? 7834;
        $cid  = $_POST['cid'] ?? 0;
        $table = \Yaf\Registry::get('database')->prefix;
        $fullTable = $table.'contents';
        $data = ContentsModel::query()
            ->with([
                'relationships' => function ($query) {
                    $query->with('meta');
                },
            ])
            ->selectRaw("$fullTable.cid,title,created,`order`,type,status,commentsNum,is_home,home_top,is_slice,authorId,`text`")
            ->with('fields', 'author')
            ->when($cid, function ($query, $cid) {
                $query->where('cid', '>', $cid);
            })
            ->whereIn('status', [ContentsModel::STATUS_PUBLISH, ContentsModel::STATUS_SECRET])
            ->where('type', ContentsModel::TYPE_POST)
            ->where('is_slice', 1)
            ->where('app_hide', ContentsModel::APP_HIDE_NO)
            ->when($mid == 7834, function ($query, $mid) {
                $query
                    ->where('relationships.mid', $mid)
                    ->join('relationships', 'relationships.cid', 'contents.cid');
            })
            ->orderBy('created')
            ->forPage($p, $l)
            ->get()
            ->each(function (ContentsModel $model) {
                $model->loadTagWithCategory();
            });

        exit(json_encode($data));
    }

    /**
     * 用户视频回调
     */
    public function user_mv_callbackAction()
    {
        $data = $this->sliceCbData();

        /** @var UserUploadModel $media */
        $media = UserUploadModel::where('id', $data['mv_id'])
            ->where('slice_status', UserUploadModel::SLICE_PROCESS)
            ->first();
        if (!$media) {
            trigger_error('上架视频--没有找到:' . json_encode($data));
            exit('fail');
        }
        try {
            $data = [
                'slice_status' => UserUploadModel::SLICE_SUCCESS,
                'm3u8_url'     => $data['source'] ?? '',
                'updated_at'   => date('Y-m-d H:i:s'),
            ];
            if ($media->update($data) <= 0) {
                throw new Exception('系统异常');
            }
        } catch (Exception $exception) {
            //\DB::rollBack();
            trigger_error('上架视频--处理失败：' . $exception->getMessage());
            exit('fail');
        }
        exit('success');
    }

    /**
     * 用户视频回调
     */
    public function ai_tyAction()
    {
        trigger_log("ai_ty action start ...");
        $service = new \service\AiSdkService();
        $service->strip_back();
        return $this->getResponse()->setBody('success');
    }

    public function change_faceAction()
    {
        trigger_log("change_face action start ...");
        $service = new \service\AiSdkService();
        $service->image_face_back();
        //errLog('收到换脸回调'.print_r($_POST,true));
        return $this->getResponse()->setBody('success');
    }

    /***
     * 资源中心同步到情报站
     * @return bool|mixed
     */
    public function center2videoAction()
    {
        $m=$param = $_POST;
        errLog("接收到资源中心的视频数据->video" . print_r($param, true));
        if (empty($param)){
            return $this->getResponse()->setBody('failed');
        }
        $exists = \VideosModel::query()
            ->where('user_uuid', $param['uuid'])
            ->where('title', $param['title'])->exists();
        if ($exists) {
            errLog("接收到资源中心的视频数据已存在");
            return $this->getResponse()->setBody('success');
        } else {
            $video = \VideosModel::create([
                '_id' => $m["_id"],
                '_id_hash' => $m["_id"],
                'user_uuid' => $m["uuid"]??"",
                'title' => $m["title"],
                'source_240' => $m["source"],
                'v_ext' => "v_ext",
                'duration' => $m["duration"]??0,
                'cover_thumb' => $m["cover_thumb"],
                'directors' => $m["directors"]??"",
                'publisher' => $m["publisher"]??"",
                'via' => $m["via"]??"user_upload",
                'coins' => $m["coins"]??0,
                'created_at' => TIMESTAMP,
                'like_ct' => rand(500,5000),
                'favorite_ct' => rand(50,15000),
                'favorite_fct' => rand(500,5000),
                'view_ct' => rand(15000,50000),
                'view_fct' => rand(15000,50000),
                'updated_at' => TIMESTAMP,
                'refresh_at' => TIMESTAMP
            ]);
            $tags = explode(',', $m["tags"]);
            if (is_array($tags)) {
                foreach ($tags as $tag) {
                    $tagsModel = \TagsModel::query()
                        ->where('name', $tag)
                        ->where('type', 1)
                        ->first();
                    if ($tagsModel) {
                        \VideosTagsModel::insert([
                            'video_id' => $video->id,
                            'tag_id' => $tagsModel->id
                        ]);
                    }else{
                        $tag_id = \TagsModel::insertGetId([
                            "name"=>$tag,
                            "type"=>1,
                        ]);
                        \VideosTagsModel::insert([
                            'video_id' =>$video->id,
                            'tag_id' => $tag_id
                        ]);
                    }
                }
            }
            return $this->getResponse()->setBody('success');
        }
    }

}
{%include file="header.tpl"%}
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<style>.layui-form.form-dialog .layui-input-block {
        margin-right: 30px
    }</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">管理</div>
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">

                        <div class="layui-inline">
                            <label class="layui-form-label">cid</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[cid]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">发布者id</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[authorId]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">标题</label>
                            <div class="layui-input-block">
                                <input type="text" name="like[title]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select name="where[status]">
                                    <option value="">全部</option>
                                    {%html_options selected=data_get($get,'where.status') options=ContentsModel::STATUS%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">类型</label>
                            <div class="layui-input-block">
                                <select name="where[type]">
                                    <option value="">全部</option>
                                    {%html_options selected=data_get($get,'where.type') options=ContentsModel::TYPE%}
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">显示浏览数</label>
                            <div class="layui-input-block">
                                <select name="orderBy[fake_view]">
                                    <option value="">无</option>
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">真实浏览数</label>
                            <div class="layui-input-block">
                                <select name="orderBy[view]">
                                    <option value="">无</option>
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                        </div>
                    </div>
                </div>


                <div class="layui-card-body">
                    <table class="layui-table"
                           lay-data="{url:'{%url('listAjax')%}', page:true, id:'test',toolbar:'#toolbar'}"
                           lay-filter="table-toolbar">
                        <thead>
                        <tr>
                            <th lay-data="{type:'checkbox'}"></th>
                            <th lay-data="{field:'cid',width: 100}">cid</th>
                            <th lay-data="{field:'home_top',width: 140 ,templet:'#a4'}">home_top</th>
                            {%*                            <th lay-data="{field:'order'}">order</th>*%}
                            {%*                            <th lay-data="{field:'parent'}">parent</th>*%}
                            {%*                            <th lay-data="{field:'password'}">password</th>*%}
                            {%*                            <th lay-data="{field:'slug'}">slug</th>*%}
                            {%*                            <th lay-data="{field:'template'}">template</th>*%}
                            <th lay-data="{field:'aa' ,templet:'#a5'}">text</th>
                            <th lay-data="{field:'fake_view',width: 130, edit:true}">显示浏览量</th>
                            <th lay-data="{field:'view',width: 140,templet:'#a6'}">APP数据</th>
                            <th lay-data="{field:'title',width: 130 ,templet:'#a3'}">属性</th>
                            <th lay-data="{field:'type',width: 130 ,templet:'#a2'}">状态</th>
                            <th lay-data="{field:'type',width: 185 ,templet:'#a1'}">时间</th>
                            <th lay-data="{fixed: 'right',width: 160 ,align:'center', toolbar: '#operate-toolbar'}">操作
                            </th>
                        </tr>
                        </thead>
                    </table>
                    <script type="text/html" id="a5">
                        <p style="white-space: pre-wrap;color: #467B96; ">{{=d.title}}</p>
                        {%*                        <hr>*%}
                        {%*                        <p>{{=d.text}}</p>*%}
                    </script>
                    <script type="text/html" id="a4">
                        置顶：{{d.home_top}} <br>
                        首页显示：{{d.is_home}} <br>
                        切片状态：{{d.is_slice}} <br>
                    </script>
                    <script type="text/html" id="a6">
                        真实浏览数量：{{d.view_str}} <br>
                        点赞数量：{{d.like_num}} <br>
                        收藏数量：{{d.favorite_num}} <br>
                        评论数量：{{d.commentsNum}} <br>
                    </script>
                    <script type="text/html" id="a3">
                        允许评论：{{d.allowComment}} <br>
                        允许聚合中：{{d.allowFeed}} <br>
                        允许引用：{{d.allowPing}} <br>
                    </script>
                    <script type="text/html" id="a2">
                        类型：{{d.type}} <br>
                        状态：{{d.status}} <br>
                    </script>
                    <script type="text/html" id="a1">
                        发布：{{d.created}} <br>
                        修改：{{d.modified}}
                    </script>
                    <script type="text/html" id="toolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add">
                                添加
                            </button>
                            <button class="layui-btn layui-btn-sm" lay-event="delSelect"
                                    data-pk="cid">删除所选
                            </button>
                            <button class="layui-btn layui-btn-sm" lay-event="updateStatus" data-pk="cid">状态设置</button>
                        </div>
                    </script>
                    <script type="text/html" id="operate-toolbar">
                        <div class="operate-toolbar">
                            <a lay-event="clear_cache">清理缓存</a>
                            <br>
                            {{# if(d.app_hide) { }}
                            <a lay-event="app_hide">APP显示</a>
                            {{# } else { }}
                            <a lay-event="app_hide">APP隐藏</a>
                            {{# } }}
                            <br>
                            {{# if(d.web_show) { }}
                            <a lay-event="web_show">Web隐藏</a>
                            {{# } else { }}
                            <a lay-event="web_show">Web显示</a>
                            {{# } }}
                            <br>
                            <a href="javascript:;" lay-event="setType"><span style="color: red">类型设置</span></a>
                            <br>
                            <a lay-event="edit">修改</a> |
                            {{# if(d.type == 'big_went' || d.type == 'skits') { }}
                            <a lay-event="special_edit">特殊修改</a>|
                            {{# } }}
                            <a data-pk="{{=d.id}}" lay-event="del">删除</a>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" class="data-dialog" id="special-edit-dialog">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>信息</legend>
    </fieldset>
    <form class="layui-form form-dialog" action="" lay-filter="form-save">
        <input type="hidden" name="cid" value="{{=d.cid}}">

        <div class="layui-form-item">
            <label class="layui-form-label">标题：</label>
            <div class="layui-input-block">

                <input placeholder="标题" name="title"
                       value="{{=d.title }}" class="layui-input">

            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">发布时间：</label>
                <div class="layui-input-inline">
                    <input type="text" name="created" class="layui-input x-date-time" placeholder="yyyy-MM-dd HH:mm:ss" value="{{=d.created}}">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">分类：</label>
            <div class="layui-input-block">
                {{#  layui.each(themes, function(index, item){ }}
                <input type="checkbox" name="category_ids[]" lay-skin="primary" title="{{item.name}}" value="{{item.id}}"
                        {{# if ((d.category_ids ? d.category_ids:[]).indexOf(item.id)!=-1) { }}
                       checked=""
                       {{#  } }}
                >
                {{#  }); }}
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">标签：</label>
            <div class="layui-input-block">

                <input placeholder="标签" name="tags"
                       value="{{=d.tags_str }}" class="layui-input">

            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">封面图：</label>
                <div class="layui-input-inline">
                    {%html_upload name='banner' src='banner' value='banner'%}
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">是否热搜 ：</label>
                <div class="layui-input-inline">
                    <select name="hotSearch" data-value="{{=d.hotSearch }}">
                        {%html_options options=[0 => '否', 1=> '是']%}
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="hidden" name="_pk" value="{{=d.cid}}">
            <button class="layui-btn submit" lay-submit="" lay-filter="save"></button>
        </div>

    </form>
</script>

<script type="text/html" class="data-dialog" id="setType">
    <form class="layui-form form-dialog" action="" lay-filter="form-save" style="margin-top: 20px">
        <input type="hidden" name="cid" value="{{=d.cid}}">
        <div class="layui-form-item">
            <label class="layui-form-label">类型:</label>
            <div class="layui-input-block">
                <select name="type" id="">
                    {%html_options options=ContentsModel::TYPE%}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">短剧/大事件ID:</label>
            <div class="layui-input-block">
                <input lay-verify="required" placeholder="短剧/大事件ID" name="sid" class="layui-input">
            </div>
        </div>
    </form>
</script>

<script type="text/html" class="data-dialog" id="batchUpdateStatus">
    <form class="layui-form form-dialog" action="" lay-filter="form-save" style="margin-top: 20px">
        <div class="layui-form-item">
            <label class="layui-form-label">状态:</label>
            <div class="layui-input-block">
                <select name="status" id="batch-update-status">
                    {%html_options options=ContentsModel::STATUS%}
                </select>
            </div>
        </div>
    </form>
</script>

{%include file="fooler.tpl"%}
<script>
    var themes = eval({%$theme_json%});

    layui.use(['table', 'laytpl', 'form', 'lazy', 'laydate', 'layedit', 'upload', 'jquery'],
        function (table, laytpl, form, lazy, layDate, layEdit, upload, $) {
            $ = typeof ($) === "undefined" ? window.$ : $;
            let verify = {}

                table.on('tool(table-toolbar)', function (obj) {
                    //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data,
                        layEvent = obj.event,
                        that = this;
                    switch (layEvent) {
                        case 'del':
                            layer.confirm('真的删除吗?', function (index) {
                                layer.close(index);
                                $.post("{%url('del')%}", {"_pk": $(that).data('pk')})
                                    .then(function (json) {
                                        if (json.code) {
                                            Util.msgErr(json.msg);
                                        } else {
                                            Util.msgOk(json.msg);
                                            obj.del();
                                        }
                                    })
                            });
                            break;
                        case 'app_hide':
                            layer.confirm('确认操作嘛?', function (index) {
                                layer.close(index);
                                $.post("{%url('app_hide')%}", {"id": data.cid})
                                    .then(function (json) {
                                        if (json.code) {
                                            Util.msgErr(json.msg);
                                        } else {
                                            Util.msgOk(json.msg);
                                        }
                                    })
                            });
                            break;
                        case 'clear_cache':
                            layer.confirm('确认操作嘛?', function (index) {
                                layer.close(index);
                                $.post("{%url('clear_by_id')%}", {"id": data.cid})
                                    .then(function (json) {
                                        if (json.code) {
                                            Util.msgErr(json.msg);
                                        } else {
                                            Util.msgOk(json.msg);
                                        }
                                    })
                            });
                            break;
                        case 'web_show':
                            layer.confirm('确认操作嘛?', function (index) {
                                layer.close(index);
                                $.post("{%url('web_show')%}", {"id": data.cid})
                                    .then(function (json) {
                                        if (json.code) {
                                            Util.msgErr(json.msg);
                                        } else {
                                            Util.msgOk(json.msg);
                                        }
                                    })
                            });
                            break;
                        case 'edit':
                            layer.open({
                                type: 2,
                                title: "sns后台管理",
                                area: [document.body.offsetWidth + 'px', (document.body.offsetHeight ) + 'px'],
                                content: 'https://webstaff.cggo.life/admin-nnnppp2/write-post.php?cid='+data.cid,
                                zIndex: layer.zIndex, //重点1
                                success: function(layero, index){
                                    layer.full(index);
                                }
                            });
                            break;
                        case 'special_edit':
                            lazy('#special-edit-dialog')
                                .data(data)
                                .width(1000)
                                .dialog(function (id, ele) {
                                    let from = $(ele).find('form')
                                    $.post("{%url('specialEdit')%}", from.serializeArray())
                                        .then(function (json) {
                                            layer.close(id);
                                            if (json.code) {
                                                return Util.msgErr(json.msg);
                                            }
                                            //添加
                                            Util.msgOk(json.msg);
                                            table.reload('test')
                                        })
                                })
                                .laytpl(function () {
                                    $('.x-date-time').each(function (key, item) {
                                        layDate.render({elem: item, 'type': 'datetime'});
                                    });
                                    xx.renderSelect(data, $, form);
                                    Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                                    form.render()
                                });
                            break;
                        case 'setType':
                            lazy('#setType')
                                .offset('auto')
                                .data(data)
                                .title('类型设置')
                                .area(['800px', '450px'])
                                .dialog(function (id, ele) {
                                    let type = $('select[name=type]').val();
                                    let cid = $('input[name=cid]').val();
                                    let sid = $('input[name=sid]').val();
                                    $.post("{%url('setType')%}", {"cid" : cid, "type" : type, "sid" : sid})
                                        .then(function (json) {
                                            layer.close(id);
                                            if (json.code) {
                                                Util.msgErr(json.msg);
                                            } else {
                                                Util.msgOk(json.msg);
                                                table.reload('test')
                                            }
                                        })
                                })
                                .laytpl(function () {
                                    xx.renderSelect(data, $, form);
                                });
                            break;
                    }
                })

            //监听头工具栏事件
            table.on('toolbar(table-toolbar)', function (obj) {
                let layEvent = obj.event,
                    checkStatus = table.checkStatus(obj.config.id),
                    data = checkStatus.data,
                    pkValAry = [],
                    pkName = $(this).data('pk');
                for (let i = 0; i < data.length; i++) {
                    if (typeof (data[i][pkName]) !== "undefined") {
                        pkValAry.push(data[i][pkName])
                    }
                }
                switch (layEvent) {
                    case 'add':
                        layer.open({
                            type: 2,
                            title: "sns后台管理",
                            area: [document.body.offsetWidth + 'px', document.body.offsetHeight + 'px'],
                            content: 'https://webstaff.cggo.life/admin-nnnppp2/write-post.php',
                            zIndex: layer.zIndex, //重点1
                            success: function(layero, index){
                                layer.full(index);
                            }
                        });
                        break;
                    case 'delSelect':
                        if (pkValAry.length === 0) {
                            return Util.msgErr('请先选择行');
                        }
                        layer.confirm('真的删除吗?', function (index) {
                            layer.close(index);
                            $.post("{%url('delAll')%}", {"value": pkValAry.join(',')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                        table.reload('test');
                                    }
                                })
                        });
                        break;
                    case 'updateStatus':
                        if (pkValAry.length === 0) {
                            return Util.msgErr('请先选择行');
                        }
                        lazy('#batchUpdateStatus')
                            .offset('auto')
                            .data(data)
                            .title('批量设置状态')
                            .area(['800px', '450px'])
                            .dialog(function (id, ele) {
                                let status = $('select[name=status]').val();
                                $.post("{%url('batchSetStatus')%}", {"pks_" : pkValAry.join(','), "status" : status})
                                    .then(function (json) {
                                        layer.close(id);
                                        if (json.code) {
                                            Util.msgErr(json.msg);
                                        } else {
                                            Util.msgOk(json.msg);
                                            table.reload('test')
                                        }
                                    })
                            })
                            .laytpl(function () {
                                xx.renderSelect(data, $, form);
                            });
                        break;
                }
            });
            // 监听单元格编辑
            table.on('edit(table-toolbar)', function (obj) {
                let data = {'_pk': obj.data['cid']}
                    data[obj.field] = obj.value;
                $.post("{%url('save')%}", data).then(function (json) {
                    layer.msg(json.msg);
                });
            });

            function dialogCallback(id, ele, obj) {
                let from = $(ele).find('form')
                $.post("{%url('save')%}", from.serializeArray())
                    .then(function (json) {
                        layer.close(id);
                        if (json.code) {
                            return Util.msgErr(json.msg);
                        }
                        if (typeof (obj) == "undefined") {
                            //添加
                            Util.msgOk(json.msg);
                            table.reload('test')
                        } else {
                            //修改
                            obj.update(json.data);
                            let index = $(obj.tr).data('index')
                            table.cache['test'][index] = json.data;
                            Util.msgOk(json.msg);
                        }
                    })
            }

            form.on('submit(search)', function (data) {
                var where = {}, ary = data.field, k;
                for (k in ary) {
                    if (ary.hasOwnProperty(k) && ary[k].length > 0) {
                        if (k.substring(k.length - 4) === 'Time' && /^\d{4}-\d{2}-\d{2}$/.test(ary[k])) {
                            ary[k] += " 00:00:00";
                        }
                        where[k] = ary[k];
                    } else {
                        where[k] = "__undefined__"
                    }
                }
                table.reload('test', {
                    where: where,
                    page: {curr: 1}
                });
                return false;
            });

            //渲染日期
            $('.x-date-time').each(function (key, item) {
                layDate.render({elem: item, 'type': 'datetime'});
            });
            $('.x-date').each(function (key, item) {
                layDate.render({elem: item});
            });
            form.verify(verify);
            layEdit.set({uploadImage: {url: Util.config("editUpload", '')}});
        })
</script>

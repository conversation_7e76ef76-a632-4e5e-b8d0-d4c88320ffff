{%include file="header.tpl"%}


<style>
    .layui-form.user-edit-dialog .layui-input-block {
        margin-right: 30px
    }
</style>
<div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
        <a lay-href="">主页</a>
        <a><cite>组件</cite></a>
        <a><cite>数据表格</cite></a>
        <a><cite>开启头部工具栏</cite></a>
    </div>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">管理</div>

                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">

                        <div class="layui-inline">
                            <label class="layui-form-label">ID</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[id]" placeholder="请输入视频ID" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label"> 标题 + 标签 </label>
                            <div class="layui-input-block">
                                <input type="text" name="like[video_title]" placeholder="请输入视频标题" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">最新上市</label>
                            <div class="layui-input-block">
                                <select name="where[top_status]" id="">
                                    <option value="">全部</option>
                                    {%html_options options=['1'=>'是' , '0'=>'否']%}
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">热度排序</label>
                            <div class="layui-input-block">
                                <select name="orderBy[video_hot]" lay-search="">
                                    <option value="">无</option>
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">时间排序</label>
                            <div class="layui-input-block">
                                <select name="orderBy[created_at]" lay-search="">
                                    <option value="">无</option>
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select name="where[status]" id="">
                                    <option value="">全部</option>
                                    {%html_options options=VideosModel::SHOW_STATUS%}
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">主题</label>
                            <div class="layui-input-block">
                                <select name="where[theme_id]" id="">
                                    <option value="">全部</option>
                                    {%html_options options=$themeSelect%}
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">时间</label>
                            <div class="layui-input-block">
                                {%html_between name="created_at" %}
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="layui-card-body">
                    <table class="layui-table"
                           lay-data="{url:'{%url("listAjax")%}', page:true, id:'test',
                           limit:10,limits:[10,20,30,40,50,60,70,80,90,100],toolbar:'#toolbar'}"
                           lay-filter="table-toolbar">
                        <thead>
                        <tr>
                            <th lay-data="{type: 'checkbox', align: 'center'}">ID</th>
                            <th lay-data="{minWidth:80 ,field:'id', align: 'center'}">ID</th>
                            <th lay-data="{minWidth:420 ,field:'av_desc', align: 'left'}">av信息</th>
                            <th lay-data="{minWidth:100,templet:'#attr2-x',align:'center'}">状态</th>
                            <th lay-data="{minWidth:150,templet:'#att2', align: 'center'}">封面图</th>
                            <th lay-data="{minWidth:120 ,field:'total', align: 'left'}">统计</th>
                            <th lay-data="{minWidth:240 ,field:'time_desc', align: 'left'}">时间</th>
                            <th lay-data="{width: 300, title: '操作', toolbar: '#operate-toolbar'}">操作</th>
                        </tr>
                        </thead>
                    </table>
                    <script type="text/html" id="toolbar">

                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="delSelect" data-pk="id">
                                删除选择
                            </button>

                            <button class="layui-btn layui-btn-sm" lay-event="save_branch"
                                    data-pk="id" data-filed="status" data-value="1">
                                上架
                            </button>

                            <button class="layui-btn layui-btn-sm" lay-event="save_branch"
                                    data-pk="id" data-filed="status" data-value="0" >
                                下架
                            </button>

                            <button class="layui-btn layui-btn-sm" lay-event="save_branch"
                                    data-pk="id" data-filed="created_at"  data-value="1"
                            >
                                更新时间
                            </button>

                            <button class="layui-btn layui-btn-sm" lay-event="refreshCache"
                                    data-pk="id" data-filed="hd">
                                刷新列表缓存
                            </button>
                            <button class="layui-btn layui-btn-sm" lay-event="theme"
                                    data-pk="id" >
                                绑定主题
                            </button>
                        </div>


                    </script>
                    <script type="text/html" id="operate-toolbar">

                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="play"
                           lay-form="play-dialog"
                        >
                            <i class="layui-icon layui-icon-play"></i>播放
                        </a>

                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"
                           lay-form="user-edit-dialog">
                            <i class="layui-icon layui-icon-edit"></i>修改</a>

                        <a class="layui-btn layui-btn-normal layui-btn-xs" data-pk="{{=d.id}}" lay-event="refresh">
                            <i class="layui-icon layui-icon-dialog"></i>更新时间</a>

                        <a class="layui-btn layui-btn-danger layui-btn-xs" data-pk="{{=d.id}}" lay-event="del">
                            <i class="layui-icon layui-icon-delete"></i>删除</a>



                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加一个DIV容器用于播放器 -->
<div id="play-dialog" style="display:none;">
    <div id="dplayer-container" style="width: 100%; height: 100%;"></div>
</div>

<script src="https://cdn.jsdelivr.net/npm/dplayer/dist/DPlayer.min.js"></script>
<script src="/static/js/hls.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer/dist/DPlayer.min.css">


<script type="text/html" id="att2">

    <img style="max-width: 120px;max-height: 120px;margin-bottom: 1px;" onclick="clickShowImage(this)"
         src="{{=d.first_img_src}}"><br>
</script>

<script type="text/html" id="attr2-x">
    <p style="margin-top: 6px">
        <input style="margin-top:3px;" type='checkbox' {{ d.status == 1 ? 'checked' : ''}}
               name="status" id="{{d.id}}"  lay-skin='switch'  lay-text='上架|下架' lay-filter="video_save">
    <p>
</script>


<script type="text/html" id="user-edit-dialog" data-h="780" data-w="900" layer-dialog="确认,取消">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>视频修改</legend>
    </fieldset>

    <form class="layui-form" action="" lay-filter="form-save">

        <div class="layui-form-item" style="width: 900px;">
            <div class="layui-inline">
                <label class="layui-form-label">标题：</label>
                <div class="layui-input-inline">
                    <input placeholder="视频标题" style="width: 900px;"
                           name="title" value="{{=d.title }}" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item" style="width: 900px;">
            <div class="layui-inline">
                <label class="layui-form-label">女优：</label>
                <div class="layui-input-inline">
                    <input placeholder="视频标题" style="width: 900px;"
                           name="actor_name" value="{{=d.actor_name }}" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item" style="width: 900px;">
            <div class="layui-inline">
                <label class="layui-form-label">标签：</label>
                <div class="layui-input-inline">
                    <input placeholder="视频标题" style="width: 900px;"
                           name="tag_name" value="{{=d.tag_name }}" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-inline" style="margin-bottom: 10px;">
            <label class="layui-form-label">封面图片：</label>
            <div class="layui-input-inline">
                {%html_upload name='cover_thumb' src='first_img_src' value='cover_thumb'%}
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">输入文案内容：</label>
                <div class="layui-input-inline" style="width: 750px;">
                    <textarea  class="layui-textarea"  rows="2"
                               id="inputField" placeholder="请输入gpt提问..." style="white-space: pre-line" >{{=d.video_title}}给前面的标题写一个140个字符左右色情点的描述，包含关键词：
                        {{=d.tags}}
                    </textarea>
                </div>
                <!-- 按钮 -->
                <button type="button" class="layui-btn" id="gpt" style="margin-left: 10px;">
                    生成gpt描述
                </button>

            </div>
        </div>


        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">视频描述：</label>
                <div class="layui-input-inline">
                        <textarea lay-verify="required" placeholder="视频描述" name="descriptions"
                                  value="{{=d.descriptions}}" class="layui-input" style="width: 900px; height: 300px;">
                        {{=d.descriptions}}
                        </textarea>
                    </label>
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="hidden" name="_pk" value="{{=d.id}}">
            <button class="layui-btn submit" lay-submit="" lay-filter="save"></button>
        </div>

    </form>
</script>

<script type="text/html" id="theme-dialog" data-h="450" data-w="480" layer-dialog="确认,取消"
        data-option="{title:'保存信息',closeBtn:false,shadeClose:true,anim:3,full:false}">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>信息</legend>

    </fieldset>
</script>

{%include file="fooler.tpl"%}

<script>
    $(document).ready(function() {
        $(document).on('click','.copy-btn', function() {
            // 获取按钮的自定义数据 content
            var contentToCopy = $(this).data('content');

            // 创建临时的 textarea 元素，用于复制内容
            var textarea = document.createElement('textarea');
            textarea.value = contentToCopy;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px'; // 将 textarea 移出屏幕
            document.body.appendChild(textarea);

            // 选择文本并复制
            textarea.select();
            document.execCommand('copy');

            // 移除 textarea 元素
            document.body.removeChild(textarea);

            // 提示复制成功（可选）
            layui.layer.msg('已复制内容: ' + contentToCopy, {icon: 1});
        });
    });
</script>

<script>
    layui.use(['table', 'laytpl', 'form', 'lazy', 'laydate', 'layedit', 'upload', 'jquery'], function (table, laytpl, form, lazy, layDate, layEdit) {

        let verify = {}

        form.on('submit(search)', function (data) {
            var where = {}, ary = data.field, k;
            for (k in ary) {
                if (ary.hasOwnProperty(k) && ary[k].length > 0) {
                    if (k.substring(k.length - 4) === 'Time' && /^\d{4}-\d{2}-\d{2}$/.test(ary[k])) {
                        ary[k] += " 00:00:00";
                    }
                    where[k] = ary[k];
                } else {
                    where[k] = "__undefined__"
                }
            }
            table.reload('test', {
                where: where,
                page: {curr: 1}
            });
            return false;
        });

        // field -- original-原创 hd-高清 top_status-置顶
        form.on('switch(video_save)', function (obj) {
            let data = {'_pk': this.id};
            data[this.name] = this.checked ? 1 : 0;
            // console.log('data...', data);

            $.post("{%url('save')%}", data).then(function (json) {
                if (json.code) {
                    return Util.msgErr(json.msg);
                }else{
                    Util.msgOk(json.msg);
                    // table.reload('test');
                }
            });
        });

        $(document).ready(function() {
            $(document).on('click','#gpt', function() {
                console.log('gpt btn....')
                // 获取输入框的值
                var inputValue = $('#inputField').val();
                console.log('gpt btn....inputValue...', inputValue);
                if(inputValue)
                {
                    let data = {'content' : inputValue};
                    $.post("{%url('video/gpt')%}", data).then(function (json) {
                        // console.log(json);
                        var text_content = json.data.text;
                        if(text_content)
                        {
                            $('textarea[name="description"]').val(text_content);
                        }
                    });
                }
            });
        });

        table.on('tool(table-toolbar)', function (obj) {
            //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            var data = obj.data,
                layEvent = obj.event,
                that = this;
            switch (layEvent) {
                case 'del':
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('del')%}", {"_pk": $(that).data('pk')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    obj.del();
                                }
                            })
                    });
                    break;

                case 'refresh': // 刷新视频时间
                    layer.confirm('真的刷新时间吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('save')%}", {"_pk": $(that).data('pk'), "created_at":1})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
                case 'edit':
                    lazy('#user-edit-dialog')
                        .data(data)
                        .width(1200)
                        .dialog(function (id, ele) {
                            dialogCallback(id, ele, obj)
                        })
                        .laytpl(function () {
                            xx.renderSelect(data, $, form);
                            Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                            layui.use('form', function () {
                                var form = layui.form
                                form.render()
                            })

                        });
                    break;
                case 'play':
                    let  play_url =  data.play_url;

                    layer.open({
                        type: 1, // 页面层
                        title: '播放视频',
                        shadeClose: true, // 禁止点击遮罩层关闭，确保只有关闭按钮关闭弹框
                        closeBtn: 1,
                        area: ['800px', '450px'], // 弹框宽高
                        content: document.getElementById('play-dialog').innerHTML, // 将播放容器加载到弹框中
                        anim: 3, // 动画效果
                        success: function (layero, index) {
                            // 初始化播放器
                            const dp = new DPlayer({
                                container: layero.find('#dplayer-container').get(0),
                                autoplay: true, // 是否自动播放
                                video: {
                                    url: play_url, // 传入的m3u8地址
                                    type: 'hls', // HLS协议播放
                                }
                            });
                        }
                    });
                    break;
            }
        })

        //监听头工具栏事件
        table.on('toolbar(table-toolbar)', function (obj) {
            var layEvent = obj.event;

            switch (layEvent) {
                case 'add':
                    lazy('#user-edit-dialog')
                        .dialog(function (id, ele) {
                            dialogCallback(id, ele)
                        })
                        .laytpl(function () {
                            xx.renderSelect({}, $, form);
                        });
                    break;
                case 'delSelect':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    //pkValAry.join(',')
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('delAll')%}", {"value": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
                case 'refreshBatch': //批量刷新视频时间
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }

                    $.post("{%url('saveBranch')%}", {"_pk": pkValAry.join(','), "created_at":1})
                        .then(function (json) {
                            if (json.code) {
                                Util.msgErr(json.msg);
                            } else {
                                Util.msgOk(json.msg);
                                table.reload('test');
                            }
                        })
                    break;
                case 'refreshCache':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }

                    $.post("{%url('refreshCache')%}", {"id": pkValAry.join(',')})
                        .then(function (json) {
                            if (json.code) {
                                Util.msgErr(json.msg);
                            } else {
                                Util.msgOk(json.msg);
                                table.reload('test');
                            }
                        })
                    break;
                case 'save_branch':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');

                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }

                    var filed = $(this).data('filed');
                    var value = $(this).data('value');

                    $.post("{%url('saveBranch')%}", {
                        "_pk": pkValAry.join(','),
                        [`${filed}`]:value
                    }).then(function (json) {
                        if (json.code) {
                            Util.msgErr(json.msg);
                        } else {
                            Util.msgOk(json.msg);
                            table.reload('test');
                        }
                    })
                    break;
                case 'theme':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }

                    var ids = pkValAry.join(',')
                    let themes = {%$themes%};
                    // console.log('themes...', themes);
                    // console.log('ids...', ids);
                    layer.open({
                        type: 1, // 页面层
                        title: '绑定主题',
                        shadeClose: true, // 禁止点击遮罩层关闭，确保只有关闭按钮关闭弹框
                        closeBtn: 1,
                        area: ['900px', '280px'], // 弹框宽高
                        anim: 3, // 动画效果
                        content: `<form class="layui-form" lay-filter="form-save" id="edit-theme">
                <div class="layui-form-item"  style="margin-top:20px;">
                    <label class="layui-form-label">主题版块：</label>
                    <div class="layui-input-block">
                        ${themes.map(v => `
                        <input type="checkbox" name="theme_id[]" value="${v.id}" title="${v.name}">
                        <div class="layui-unselect layui-form-checkbox">
                        <span>${v.name}</span>
                        <i class="layui-icon layui-icon-ok"></i>
                        </div>
                        `).join('')}
                    </div>
                </div>

                <!-- 隐藏的 _pk 字段 -->
                <div class="layui-form-item layui-hide">
                    <input type="hidden" name="_pk" value="${ids}">
                </div>

                <!-- 保存和取消按钮 -->
                <div class="layui-layer-btn layui-layer-btn-"
                style="margin-right:100px;margin-top:30px;">
                <a class="layui-layer-btn0"  lay-submit lay-filter="save">确定</a>
                <a class="layui-layer-btn1">取消</a>
                </div>
              </form>`,
                        success: function (layero, index) {
                            layui.form.render(); // 渲染表单元素
                        }
                    });
                    break;
            }
        });

        // 监听表单提交
        form.on('submit(save)', function (data) {
            // data.field 中包含所有表单项的值
            var params = data.field;
            console.log('提交的表单数据 params：', params);

            $.post('{%url('saveBranch')%}', params)
                .then(function (json) {
                    if (json.code) {
                        return Util.msgErr(json.msg);
                    }

                    Util.msgOk(json.msg);
                    table.reload('test')
                })

            // 返回 false 可以阻止表单自动提交，避免页面跳转
            return false;
        });

        function dialogCallback(id, ele, obj) {
            let from = $(ele).find('form')
            $.post('{%url('save')%}', from.serializeArray())
                .then(function (json) {
                    layer.close(id);
                    if (json.code) {
                        return Util.msgErr(json.msg);
                    }
                    if (typeof (obj) == "undefined") {
                        Util.msgOk(json.msg);
                        table.reload('test')
                    } else {
                        // console.log('data...', json.data);
                        obj.update(json.data);
                        let index = $(obj.tr).data('index')
                        table.cache['test'][index] = json.data;
                        Util.msgOk(json.msg);
                        table.reload('test')
                    }
                })
        }

        //渲染日期
        $('.x-date-time').each(function (key, item) {
            layDate.render({elem: item, 'type': 'datetime'});
        });
        $('.x-date').each(function (key, item) {
            layDate.render({elem: item});
        });
        form.verify(verify);
        layEdit.set({uploadImage: {url: Util.config("editUpload", '')}});
    })
</script>
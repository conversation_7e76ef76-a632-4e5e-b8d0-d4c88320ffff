{%include file="header.tpl"%}
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<style>
    .layui-form.form-dialog .layui-input-block {
        margin-right: 30px
    }

</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">管理</div>
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">

                        <div class="layui-inline">
                            <label class="layui-form-label">aff</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[aff]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">邀请码</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[aff_code]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">设备</label>
                            <div class="layui-input-block">
                                <select name="where[oauth_type]">
                                    <option value="">全部</option>
                                    {%html_options options=MemberModel::TYPE%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">VIP类型</label>
                            <div class="layui-input-block">
                                <select name="where[vip_level]">
                                    <option value="">全部</option>
                                    {%html_options options=MemberModel::VIP_LEVEL%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">oauth_id</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[oauth_id]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">uuid</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[uuid]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">账户</label>
                            <div class="layui-input-block">
                                <input type="text" name="search[username]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">角色</label>
                            <div class="layui-input-block">
                                <select name="where[role_id]">
                                    <option value="">全部</option>
                                    {%html_options options=MemberModel::ROLE%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">被谁aff邀请</label>
                            <div class="layui-input-block">
                                <input type="text" name="search[invited_by]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">用户昵称</label>
                            <div class="layui-input-block">
                                <input type="text" name="search[nickname]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">渠道标识</label>
                            <div class="layui-input-block">
                                <input type="text" name="search[channel]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">注册IP</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[regip]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <!--
                        <div class="layui-inline">
                            <label class="layui-form-label">手机</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[phone]" placeholder="请输入"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        -->

                        <div class="layui-inline">
                            <label class="layui-form-label">金币范围</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" name="where[money_min]" placeholder="最小值" autocomplete="off"
                                       class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" name="where[money_max]" placeholder="最大值" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">会员到期</label>
                            <div class="layui-input-block">
                                {%html_between name='expired_at'%}
                            </div>
                        </div>


                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                        </div>
                    </div>
                </div>


                <div class="layui-card-body">
                    <table class="layui-table"
                           lay-data="{url:'{%url('listAjax')%}', page:true, id:'test'}"
                           lay-filter="table-toolbar">
                        <thead>
                        <tr>
                            <th lay-data="{field:'aff',width:80}">aff</th>
                            <th lay-data="{templet:'#att4'}">邀请码/渠道/版本</th>
                            <th lay-data="{field:'nickname',width:259,templet:'#att1'}">用户昵称</th>
                            <th lay-data="{field:'nickname',width:270,templet:'#att3'}">设备</th>
                            <th lay-data="{field:'phone',width:140,templet:'#att2'}">属性</th>
                            <th lay-data="{field:'regdate',width:170,templet:'#time-attr'}">时间</th>
                            <th lay-data="{fixed:'right',width: 160 ,align:'center', toolbar: '#operate-toolbar'}">操作
                            </th>
                        </tr>
                        </thead>
                        <script type="text/html" id="att1">
                            <div style="display: flex;flex-direction: row;align-items:center;">
                                <img style="width: 50px;height:50px;margin-right: 10px;background:#ccc;" title="头像"
                                     alt="头像" src="{{d.thumb}}" onclick="show_img('{{d.thumb}}')">
                                <div>
                                    会员昵称：{{d.nickname}} <br>
                                    会员账户：{{d.username}} <br>
                                    会员角色：{{d.role_str}} <br>
                                    会员等级：{{d.vip_level_str}}<br>
                                    会员到期：{{d.expired_at}}
                                </div>
                            </div>

                        </script>
                        <script type="text/html" id="att2">
                            金币：{{d.money}}<br>
                            订阅收益：{{d.income_money}}<br>
                            稿费收益：{{d.income_royalties}}<br>
                            积分：{{d.exp}} <br>
                            邀请人：{{d.invited_by}} <br>
                            邀请数：{{d.invited_num}} <br>
                            代理收益：{{d.proxy_money}} <br>
                            up主认证：{{d.auth_status_str}} <br>
                            提现名字:{{d.draw_name}}
                        </script>
                        <script type="text/html" id="att3">
                            {{d.uuid}}
                            <hr>
                            <span style="color: goldenrod">{{d.oauth_type}}<br>{{d.oauth_id}}<br></span>
                            {{d.regip}} <br/>
                            {{d.email}}
                        </script>
                        <script type="text/html" id="att4">
                            渠道:&nbsp;{{d.channel}} <br>
                            版本:&nbsp;{{d.app_version}} <br>
                            邀请码:&nbsp;{{d.aff_code}} <br>
                        </script>
                    </table>
                    <script type="text/html" id="toolbar">
                        <div class="layui-btn-container">
                            <!--<button class="layui-btn layui-btn-sm" lay-event="add">
                                添加
                            </button>-->
                            <!--<button class="layui-btn layui-btn-sm" lay-event="cdk">
                                兑换码
                            </button>-->
                        </div>
                    </script>
                    <script type="text/html" id="operate-toolbar">
                        <a href="javascript:;" style="color: #9B410E" onclick="feedDetail('{{d.uuid}}')">工单</a> |
                        <a href="javascript:;" style="color: #9B410E" onclick="clearCached('{{d.aff}}')">缓存</a> |
                        {{#if(d.ban_post== 1 || d.role_id == 10){ }}
                        <a href="javascript:void(0);" lay-event="unban" data-pk="{{=d.uid}}">解封</a> |
                        {{# } else { }}
                        <a href="javascript:void(0);" lay-event="ban" data-pk="{{=d.uid}}">禁言</a>|
                        {{# } }}
                        <a href="javascript:void(0);" lay-event="banAll" data-pk="{{=d.uid}}">封禁</a>
                        <br>
                        {{#if(d.auth_status== 0){ }}
                        <a href="javascript:;" style="color: #1AB394" lay-event="certified" data-pk="{{=d.uid}}">认证</a> |
                        {{# } }}
                        <a href="javascript:;" style="color: #1AB394" lay-event="edit">修改</a>&nbsp;|
                        <!--<a href="javascript:;" style="color: #1AB394" data-pk="{{=d.uid}}" lay-event="pwd">密码</a>&nbsp; -->
                        <br>
                        <a href="javascript:;" style="color: #1AB394" data-pk="{{=d.uid}}" lay-event="drawName">提现名字</a>
                        <!--  <a href="javascript:;" data-pk="{{=d.uid}}" lay-event="del">删除</a> <br> -->
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" class="data-dialog" id="user-edit-dialog">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>信息</legend>
    </fieldset>
    <form class="layui-form form-dialog" action="" lay-filter="form-save">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">用户昵称</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="用户昵称" name="nickname" value="{{=d.nickname }}"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">账号</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="账号" name="username" value="{{=d.username }}"
                           class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">用户角色</label>
                <div class="layui-input-inline">
                    <select name="role_id" data-value="{{=d.role_id }}">
                        {%html_options options=MemberModel::ROLE%}
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">头像</label>
                <div class="layui-input-inline">
                    {%html_upload name='thumb' src='thumb' value='thumb'%}
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">会员等级</label>
                <div class="layui-input-inline">
                    <select name="vip_level" data-value="{{=d.vip_level }}">
                        {%html_options options=MemberModel::VIP_LEVEL%}
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">过期时间</label>
                <div class="layui-input-inline">
                    <input placeholder="yyyy-mm-d" autocomplete="off" name="expired_at" value="{{=d.expired_at_str }}"
                           class="layui-input x-date">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">电话区号</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="电话区号" name="phone_prefix" value="{{=d.phone_prefix }}"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">电话</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="电话" name="phone" value="{{=d.phone }}"
                           class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">up主认证--暂时保留</label>
            <div class="layui-input-inline">
                <select name="auth_status" data-value="{{=d.auth_status }}">
                    {%html_options options=MemberModel::AUTH_STATUS%}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">积分</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="积分" name="exp" value="{{d.exp||0 }}"
                           class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">介绍</label>
            <div class="layui-input-block">
                {%html_textarea name='person_signnatrue' value='{{d.person_signnatrue }}'%}
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="hidden" name="_pk" value="{{=d.uid}}">
            <button class="layui-btn submit" lay-submit="" lay-filter="save"></button>
        </div>
    </form>
</script>


{%include file="fooler.tpl"%}
<script>
    function feedDetail(uuid) {
        top.layer.open({
            type: 2,
            title: '反馈详情',
            shadeClose: true,
            shade: 0.4,
            area: ['1200px', '600px'],
            content: "{%url('userfeed/detail')%}?uuid=" + uuid
        });
    }

    function clearCached(aff) {
        let idx = layer.load();
        $.post("{%url('clearCached')%}", {"aff": aff})
            .then(function (json) {
                layer.close(idx);
            })
    }

    layui.use(['table', 'laytpl', 'form', 'lazy', 'laydate', 'layedit', 'upload', 'jquery'], function (table, laytpl, form, lazy, layDate, layEdit) {

        let verify = {}
            table.on('tool(table-toolbar)', function (obj) {
                //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data,
                    layEvent = obj.event,
                    that = this;
                switch (layEvent) {
                    case 'send_package':
                        top.layer.prompt({title: '赠送的包id'}, function (value, index) {
                            top.layer.close(index);
                            $.post("{%url('send_package')%}", {"aff": $(that).data('aff'), "package_id":value})
                                .then(function (json) {
                                    json.code ? Util.msgErr(json.msg) : Util.msgOk(json.msg);
                                })
                        });
                        break;
                    case 'pwd':
                        top.layer.prompt({title: '修改密码'}, function (value, index) {
                            top.layer.close(index);
                            $.post("{%url('change_pwd')%}", {"uid": $(that).data('pk'), "pwd":value})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                    }
                                })
                        });
                        break;
                    case 'drawName':
                        top.layer.prompt({title: '提现名字设置'}, function (value, index) {
                            top.layer.close(index);
                            $.post("{%url('set_drawname')%}", {"uid": $(that).data('pk'), "draw_name":value})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                    }
                                })
                        });
                        break;
                    case 'ban':
                        top.layer.prompt({
                            formType: 2,
                            value: ' ',
                            title: '请输入禁言理由',
                            area: ['300px', '150px'] //自定义文本域宽高
                        }, function (value, index) {
                            top.layer.close(index);
                            $.post("{%url('banInfo')%}", {"id": $(that).data('pk'), "reply":value})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        obj.update(json.data);
                                        let index = $(obj.tr).data('index')
                                        table.reload('test');
                                        // table.cache['test'][index] = json.data;
                                        Util.msgOk(json.msg);

                                    }
                                })
                        });
                        break;
                    case 'banAll':
                        top.layer.prompt({
                            formType: 2,
                            value: ' ',
                            title: '请输入封禁理由',
                            area: ['300px', '150px'] //自定义文本域宽高
                        }, function (value, index) {
                            top.layer.close(index);
                            $.post("{%url('ban')%}", {"id": $(that).data('pk'), "reply":value})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        obj.update(json.data);
                                        let index = $(obj.tr).data('index')
                                        table.reload('test');
                                        // table.cache['test'][index] = json.data;
                                        Util.msgOk(json.msg);

                                    }
                                })
                        });
                        break;
                    case 'certified':
                        layer.confirm('真的要认证成为创作者吗?', function (index) {
                            layer.close(index);
                            $.post("{%url('post_creator')%}", {"id": $(that).data('pk')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                        table.reload('test');
                                    }
                                })
                        });
                        break;
                    case 'unban':
                        layer.confirm('确认解除禁止该用户发布资源?', function (index) {
                            layer.close(index);
                            $.post("{%url('unbanInfo')%}", {"id": $(that).data('pk')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        obj.update(json.data);
                                        let index = $(obj.tr).data('index')
                                        table.reload('test');
                                        // table.cache['test'][index] = json.data;
                                        Util.msgOk(json.msg);
                                    }
                                })
                        });
                        break;
                    case 'del':
                        layer.confirm('真的删除吗?', function (index) {
                            layer.close(index);
                            $.post("{%url('del')%}", {"_pk": $(that).data('pk')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                        obj.del();
                                    }
                                })
                        });
                        break;
                    case 'edit':
                        lazy('#user-edit-dialog')
                            .data(data)
                            .width(900)
                            .dialog(function (id, ele) {
                                dialogCallback(id, ele, obj)
                            })
                            .laytpl(function () {
                                xx.renderSelect(data, $, form);
                                renderDateInput();
                                Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                            });
                        break;
                }
            })

        //监听头工具栏事件
        table.on('toolbar(table-toolbar)', function (obj) {
            var layEvent = obj.event;
            switch (layEvent) {
                case 'cdk':
                    top.layer.prompt({title: '生成兑换码'}, function (value, index) {
                        top.layer.close(index);
                        $.post("{%url('cdk')%}", {"num":value})
                            .then(function (json) {
                                alert(json.data.join("\r\n"));
                                console.log(json.data.join("\r\n"));
                            })
                    });
                    break;
                case 'delSelect':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('delAll')%}", {"value": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
            }
        });

        function dialogCallback(id, ele, obj, saveUrl) {
            let from = $(ele).find('form')
            $.post(saveUrl || "{%url('save')%}", from.serializeArray())
                .then(function (json) {
                    layer.close(id);
                    if (json.code) {
                        return Util.msgErr(json.msg);
                    }
                    if (typeof (obj) == "undefined") {
                        //添加
                        Util.msgOk(json.msg);
                        table.reload('test')
                    } else {
                        //修改
                        obj.update(json.data);
                        let index = $(obj.tr).data('index')
                        table.cache['test'][index] = json.data;
                        Util.msgOk(json.msg);
                        table.reload('test')
                    }
                })
        }

        form.on('submit(search)', function (data) {
            var where = {}, ary = data.field, k;
            for (k in ary) {
                if (ary.hasOwnProperty(k) && ary[k].length > 0) {
                    if (k.substring(k.length - 4) === 'Time' && /^\d{4}-\d{2}-\d{2}$/.test(ary[k])) {
                        ary[k] += " 00:00:00";
                    }
                    where[k] = ary[k];
                } else {
                    where[k] = "__undefined__"
                }
            }
            table.reload('test', {
                where: where,
                page: {curr: 1}
            });
            return false;
        });

        //渲染日期
        function renderDateInput() {
            $('.x-date-time').each(function (key, item) {
                layDate.render({elem: item, 'type': 'datetime'});
            });
            $('.x-date').each(function (key, item) {
                layDate.render({elem: item});
            });
        }

        renderDateInput();


        form.verify(verify);
        layEdit.set({uploadImage: {url: Util.config("editUpload", '')}});
    })
</script>
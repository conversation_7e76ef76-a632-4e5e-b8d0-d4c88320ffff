{%include file="header.tpl"%}
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<style>.layui-form.form-dialog .layui-input-block {
        margin-right: 30px
    }</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">管理</div>
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">

                                                    <div class="layui-inline">
                                <label class="layui-form-label">id</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[id]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">aff</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[aff]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">月卡价格</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[month]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">季卡价格</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[quarter]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">年卡价格</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[year]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">帖子数量</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[post_num]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">成员数量</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[member_num]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">月卡收益</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[month_income]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">季卡收益</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[quarter_income]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">年卡收益</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[year_income]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">创建时间</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[created_at]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">通知</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[notice]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                    <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-block">
                                                                            <input type="text" name="search[status]" placeholder="请输入"
                                               autocomplete="off" class="layui-input">
                                                                    </div>
                            </div>
                                                <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                        </div>
                    </div>
                </div>


                <div class="layui-card-body">
                    <table class="layui-table"
                           lay-data="{url:'{%url('listAjax')%}', page:true, id:'test',toolbar:'#toolbar'}"
                           lay-filter="table-toolbar">
                        <thead>
                        <tr>
                            <th lay-data="{type:'checkbox'}"></th>
                                                            <th lay-data="{field:'id'}">id</th>
                                                            <th lay-data="{field:'aff'}">aff</th>
                                                            <th lay-data="{field:'month'}">月卡价格</th>
                                                            <th lay-data="{field:'quarter'}">季卡价格</th>
                                                            <th lay-data="{field:'year'}">年卡价格</th>
                                                            <th lay-data="{field:'post_num'}">帖子数量</th>
                                                            <th lay-data="{field:'member_num'}">成员数量</th>
                                                            <th lay-data="{field:'month_income'}">月卡收益</th>
                                                            <th lay-data="{field:'quarter_income'}">季卡收益</th>
                                                            <th lay-data="{field:'year_income'}">年卡收益</th>
                                                            <th lay-data="{field:'created_at'}">创建时间</th>
                                                            <th lay-data="{field:'notice'}">通知</th>
                                                            <th lay-data="{field:'status'}">状态</th>
                                                        <th lay-data="{fixed: 'right',width: 200 ,align:'center', toolbar: '#operate-toolbar'}">操作
                            </th>
                        </tr>
                        </thead>
                    </table>
                    <script type="text/html" id="toolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add">
                                添加
                            </button>
                            <button class="layui-btn layui-btn-sm" lay-event="delSelect"
                                    data-pk="id">删除所选
                            </button>
                        </div>
                    </script>
                    <script type="text/html" id="operate-toolbar">
                        <div class="operate-toolbar">
                            <a lay-event="edit">修改</a> |
                            <a data-pk="{{=d.id}}" lay-event="del">删除</a>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/html" class="data-dialog" id="user-edit-dialog">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>信息</legend>
    </fieldset>
    <form class="layui-form form-dialog" action="" lay-filter="form-save">
                                

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">aff：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="aff" name="aff"
                                    value="{{=d.aff }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">月卡价格：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="月卡价格" name="month"
                                    value="{{=d.month }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">季卡价格：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="季卡价格" name="quarter"
                                    value="{{=d.quarter }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年卡价格：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="年卡价格" name="year"
                                    value="{{=d.year }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">帖子数量：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="帖子数量" name="post_num"
                                    value="{{=d.post_num }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">成员数量：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="成员数量" name="member_num"
                                    value="{{=d.member_num }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">月卡收益：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="月卡收益" name="month_income"
                                    value="{{=d.month_income }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">季卡收益：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="季卡收益" name="quarter_income"
                                    value="{{=d.quarter_income }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年卡收益：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="年卡收益" name="year_income"
                                    value="{{=d.year_income }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">创建时间：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="创建时间" name="created_at"
                                    value="{{=d.created_at }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">通知：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="通知" name="notice"
                                    value="{{=d.notice }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                    

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">状态：</label>
                    <div class="layui-input-inline">

                                                    <input lay-verify="required"                                    placeholder="状态" name="status"
                                    value="{{=d.status }}" class="layui-input">
                        
                    </div>
                </div>
            </div>
                <div class="layui-form-item layui-hide">
            <input type="hidden" name="_pk" value="{{=d.id}}">
            <button class="layui-btn submit" lay-submit="" lay-filter="save"></button>
        </div>

    </form>
</script>

{%include file="fooler.tpl"%}
<script>
    layui.use(['table', 'laytpl', 'form', 'lazy', 'laydate', 'layedit','upload','jquery'],
        function (table, laytpl, form, lazy, layDate, layEdit, upload, $) {
	$ = typeof ($) === "undefined" ? window.$ : $;
        let verify = {}

        table.on('tool(table-toolbar)', function (obj) {
            //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            var data = obj.data,
                layEvent = obj.event,
                that = this;
            switch (layEvent) {
                case 'del':
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('del')%}", {"_pk": $(that).data('pk')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    obj.del();
                                }
                            })
                    });
                    break;
                case 'edit':
                    lazy('#user-edit-dialog')
                        .data(data)
                        .area([1100 +'px' , document.body.offsetHeight + 'px'])
                        .dialog(function (id, ele) {
                            dialogCallback(id, ele, obj)
                        })
                        .laytpl(function () {
                            xx.renderSelect(data, $, form);
                            Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                        });
                    break;
            }
        })

        //监听头工具栏事件
        table.on('toolbar(table-toolbar)', function (obj) {
            let layEvent = obj.event,
                checkStatus = table.checkStatus(obj.config.id),
                data = checkStatus.data,
                pkValAry = [],
                pkName = $(this).data('pk');
            for (let i = 0; i < data.length; i++) {
                if (typeof (data[i][pkName]) !== "undefined") {
                    pkValAry.push(data[i][pkName])
                }
            }
            switch (layEvent) {
                case 'add':
                    lazy('#user-edit-dialog')
			.area([1100 +'px' , document.body.offsetHeight + 'px'])
                        .dialog(function (id, ele) {
                            dialogCallback(id, ele)
                        })
                        .laytpl(function () {
                            xx.renderSelect({}, $, form);
                            Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                        });
                    break;
                case 'delSelect':
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('delAll')%}", {"value": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
            }
        });
        // 监听单元格编辑
        table.on('edit(table-toolbar)', function (obj) {
            let data = {'_pk': obj.data['id']}
            data[obj.field] = obj.value;
            $.post("{%url('save')%}", data).then(function (json) {
                layer.msg(json.msg);
            });
        });

        function dialogCallback(id, ele, obj) {
            let from = $(ele).find('form')
            $.post("{%url('save')%}", from.serializeArray())
                .then(function (json) {
                    layer.close(id);
                    if (json.code) {
                        return Util.msgErr(json.msg);
                    }
                    if (typeof (obj) == "undefined") {
                        //添加
                        Util.msgOk(json.msg);
                        table.reload('test')
                    } else {
                        //修改
                        obj.update(json.data);
                        let index = $(obj.tr).data('index')
                        table.cache['test'][index] = json.data;
                        Util.msgOk(json.msg);
                    }
                })
        }

        form.on('submit(search)', function (data) {
            var where = {}, ary = data.field, k;
            for (k in ary) {
                if (ary.hasOwnProperty(k) && ary[k].length > 0) {
                    if (k.substring(k.length - 4) === 'Time' && /^\d{4}-\d{2}-\d{2}$/.test(ary[k])) {
                        ary[k] += " 00:00:00";
                    }
                    where[k] = ary[k];
                } else {
                    where[k] = "__undefined__"
                }
            }
            table.reload('test', {
                where: where,
                page: {curr: 1}
            });
            return false;
        });

        //渲染日期
        $('.x-date-time').each(function (key, item) {
            layDate.render({elem: item, 'type': 'datetime'});
        });
        $('.x-date').each(function (key, item) {
            layDate.render({elem: item});
        });
        form.verify(verify);
        layEdit.set({uploadImage: {url: Util.config("editUpload", '')}});
    })
</script>

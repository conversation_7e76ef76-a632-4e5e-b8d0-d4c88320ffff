<?php

use service\UserService;

/**
 * Class MembersController
 * <AUTHOR>
 * @date 2020-05-22 09:34:46
 */
class MembersController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    const UNBAN_USERS = [160, 130, 176,196];

    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (MemberModel $item) {
            $item->setHidden(['password'])->append(['ban_post_str']);
            $item->aff_code = generate_code($item->aff);
            $item->expired_at_str = date('Y-m-d', strtotime($item->expired_at));
            $item->vip_level_str = MemberModel::VIP_LEVEL[$item->vip_level] ?? '未知';
            $item->role_str = MemberModel::ROLE[$item->role_id] ?? '未知';
            $item->auth_status_str = MemberModel::AUTH_STATUS[$item->auth_status];
            $item->free_view_cnt_str = date('Y-m-d', time()) === $item->free_view_date ? $item->free_view_cnt : setting('free_view_cnt', 10);
            return $item;
        };
    }

    protected function whereSelectBefore(&$query)
    {
        $ip = data_get($_GET, 'where.regip');
        if (!$this->argsEmpty($ip)) {
            $query->reorder();
        }
    }

    // 禁言
    public function banInfoAction()
    {
        $id = $this->post['id'] ?? null;
        $content = $this->post['reply'] ?? '';
        $model = MemberModel::find($id);
        if (empty($model)) {
            return $this->ajaxError('用户不存在');
        }
        $update = UserService::updateUser($model->aff, ['ban_post' => 1, 'person_signnatrue' => $content]);
        if ($model->auth_status == MemberModel::AUTH_STATUS_YES){
            $creator = PostCreatorModel::findByAff($model->aff);
            if ($creator){
                $creator->ban_post = MemberModel::BAN_POST_YES;
                $creator->updated_at = \Carbon\Carbon::now();
                $creator->save();
            }
        }

        if (!$update) {
            return $this->ajaxError('更新失败');
        }
        return $this->ajaxSuccessMsg('操作成功');
    }

    // 封禁
    public function banAction(): bool
    {
        try {
            $id = $this->post['id'] ?? null;
            $content = $this->post['reply'] ?? '';
            $member = MemberModel::find($id);
            test_assert($member, '用户不存在');
            $member->ban_post = 1;
            $member->person_signnatrue = $content;
            $member->role_id = MemberModel::ROLE_BAN;
            $isOk = $member->save();
            test_assert($isOk, '系统异常');
            $member->clearCached();
            return $this->ajaxSuccessMsg('操作成功');
        } catch (Throwable $e) {
            return $this->ajaxError($e->getMessage());
        }
    }


    // 解封
    public function unbanInfoAction()
    {
        $id = $this->post['id'] ?? null;
//        if (!in_array($this->getUser()->uid, self::UNBAN_USERS)) {
//            return $this->ajaxError('你没有权限解封用户');
//        }
        if ($this->getUser()->role_id != 1){
            return $this->ajaxError('你没有权限解封用户');
        }

        $model = MemberModel::find($id);
        if (empty($model)) {
            return $this->ajaxError('用户不存在');
        }
        $update = UserService::updateUser($model->aff, ['ban_post' => 0, 'person_signnatrue' => '' ,'role_id' => MemberModel::ROLE_NORMAL]);

        if ($model->auth_status == MemberModel::AUTH_STATUS_YES){
            $creator = PostCreatorModel::findByAff($model->aff);
            if ($creator){
                $creator->ban_post = MemberModel::BAN_POST_NO;
                $creator->updated_at = \Carbon\Carbon::now();
                $creator->save();
            }
        }

        if (!$update) {
            return $this->ajaxError('更新失败');
        }
        return $this->ajaxSuccessMsg('操作成功');
    }

    // 认证成为创作者
    public function post_creatorAction()
    {
        $id = $this->post['id'] ?? null;
        $member = MemberModel::find($id);
        if (empty($member)) {
            return $this->ajaxError('用户不存在');
        }
        if ($member->auth_status == MemberModel::AUTH_STATUS_YES){
            return $this->ajaxError('用户已经是创作者～～～～');
        }
        try {
            transaction(function () use($id,$member){
                //post_creator
                $creator = \PostCreatorModel::findByAff($member->aff, true);
                if (empty($creator)) {
                    $creator = \PostCreatorModel::make();
                    $creator->aff = $member->aff;
                    $creator->nickname = $member->nickname;
                    $creator->ban_post = $member->ban_post;
                    $creator->created_at = \Carbon\Carbon::now();
                    $creator->updated_at = \Carbon\Carbon::now();
                }
                $creator->status = \MemberModel::AUTH_STATUS_YES;
                $isOk = $creator->save();
                test_assert($isOk,"系统异常，请重试");

                //修改用户表
                $member->auth_status = MemberModel::AUTH_STATUS_YES;
                $isOk = $member->save();
                test_assert($isOk,"系统异常，请重试");
            });
        } catch (Exception $e) {
            return $this->ajaxError($e->getMessage());
        }

        $member->clearCached();

        return $this->ajaxSuccessMsg('认证成功');
    }

    /**
     * 保存数据
     * @return bool
     */
    public function saveAction(): bool
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $className = $this->getModelClass();
        $pkName = $this->getPkName();
        $post = $this->postArray();
        try {
            if (!empty($post['_pk'])) {
                $where = [[$pkName, '=', $post['_pk']]];
                $model = $className::where($where)->first();
                test_assert($model, '数据不存在');
                test_assert($model->ban_post == 0, '被封禁用户信息不允许任何修改');
            }
            if ($model = $this->doSave($post)) {
                return $this->ajaxSuccessMsg('操作成功', 0, call_user_func($this->listAjaxIteration(), $model));
            } else {
                return $this->ajaxError('操作错误');
            }
        } catch (\Throwable $e) {
            return $this->ajaxError($e->getMessage());
        }
    }

    public function change_pwdAction(): bool
    {
        $id = $this->post['uid'] ?? null;
        $model = MemberModel::find($id);
        if (empty($model)) {
            return $this->ajaxError('用户不存在');
        }
        if ($model->ban_post == 1) {
            return $this->ajaxError('被封禁用户信息不允许任何修改');
        }
        $password = $_POST['pwd'] ?? null;
        if (empty($password)) {
            return $this->ajaxError('密码为空');
        }
        if (empty($model->password)) {
            return $this->ajaxError('用户没有设置密码');
        }
        $password = MemberModel::generatePassword(trim($password));
        $model->password = $password;
        $isOk = $model->save();
        if (!$isOk) {
            return $this->ajaxError('更新失败');
        }
        return $this->ajaxSuccessMsg('操作成功');
    }

    public function set_drawnameAction(): bool
    {
        $id = $this->post['uid'] ?? null;
        $model = MemberModel::find($id);
        if (empty($model)) {
            return $this->ajaxError('用户不存在');
        }
        if ($model->ban_post == 1) {
            return $this->ajaxError('被封禁用户信息不允许任何修改');
        }
        $draw_name = $_POST['draw_name'] ?? null;
        if (empty($draw_name)) {
            return $this->ajaxError('名字不能为空');
        }
        $model->draw_name = $draw_name;
        $isOk = $model->save();
        if (!$isOk) {
            return $this->ajaxError('更新失败');
        }
        return $this->ajaxSuccessMsg('操作成功');
    }

    /**
     * 试图渲染
     * @return string
     */
    public function indexAction()
    {
        $this->assignThisGlobal();
        $this->display();
    }

    protected function assignThisGlobal()
    {
        $areaArray = AreaCnModel::orderBy('sort')->where('id', '!=', 1)->where('level', '=', 2)->get([
            'id',
            'parentid',
            'areaname',
            'shortname'
        ])->toArray();
        $this->assign('areaArrayJson', json_encode($areaArray));
    }

    /** username 在post表单中，可以函数重新设置一下，也可以用异常来进行验证 username 值 */
    protected function setUsername($value, $data, $pk)
    {
        if (empty($value)) {
            return $value;
        }
        $model = MemberModel::where('username', $value)->where('uid', '!=', $pk)->first();
        if (empty($model)) { // 验证通过
            return $value;
        }
        throw new RuntimeException('此账号已经绑定过');
    }

    /** phone 在post表单中，可以函数重新设置一下，也可以用异常来进行验证 phone 值 */
    protected function setPhone($value, $data, $pk)
    {
        if (empty($value)) {
            return $value;
        }
        $model = MemberModel::where('phone', $value)->where('uid', '!=', $pk)->first();
        if (empty($model)) { // 验证通过
            return $value;
        }
        throw new RuntimeException('此号码已经绑定过');
    }

    protected function saveAfterCallback($model, $oldModel = null)
    {
        $member = $model;

        redis()->del(MemberModel::USER_REIDS_PREFIX . md5($member->oauth_type . $member->oauth_id));
        redis()->del(MemberModel::USER_REIDS_PREFIX . $member->aff);
    }

    // 清除缓存
    public function clearCachedAction()
    {
        $aff = intval($this->post['aff']);
        \service\UserService::clearCache($aff);
    }

    protected function formatSearchVal($columnName, $val)
    {
        if ($columnName == 'aff_code') {
            $val = get_num($val);
        }
        return trim($val);
    }

    protected function formatKey($key, $value)
    {
        if (!preg_match_all("#^([a-zA-Z_\d]+)$#i", trim($key))) {
            return [false, $value];
        }
        if ($key == 'aff_code') {
            $key = 'aff';
        }
        return [$key, $value];
    }

    protected function getSearchWhereParam()
    {
        $get = $this->getRequest()->getQuery();
        $get['where'] = $get['where'] ?? [];
        $where = [];
        foreach ($get['where'] as $key => $value) {
            if ($value === '__undefined__') {
                continue;
            }
            $value = $this->formatSearchVal($key, $value);

            list($key, $value) = $this->formatKey($key, $value);
            if (empty($key)) {
                continue;
            }
            if ($value !== '' && $key != 'money_min' && $key != 'money_max') {
                $where[] = [$key, '=', $value];
            } elseif ($key == 'money_min') {
                $where[] = ['money', '>=', $value];
            } elseif ($key == 'money_max') {
                $where[] = ['money', '<=', $value];
            }

        }
        return $where;
    }

    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
        return MemberModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'uid'; // views层也是用的uid，所以不要修改
    }

    /**
     * 定义数据操作日志
     * @return string
     * <AUTHOR>
     */
    protected function getLogDesc(): string
    {
        return '';
    }
}
<?php

/**
 * Class AvvideoController
 * @date 2020-01-17 18:57:38
 */
class AvvideoController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    /**
     * 获取列表数据
     */
    public function listAjaxAction()
    {
        if (!$this->getRequest()->isXmlHttpRequest()) {
            return $this->ajaxError('加载错误');
        }

        // \DB::enableQueryLog();
        $pkName = $this->getPkName();
        /** @var \Illuminate\Database\Eloquent\Builder $modelBuilder */
        $modelBuilder = $this->getModelObject();
        $orderBy = $this->listAjaxOrder();
        if (empty($orderBy)) {
            $modelBuilder->orderByDesc('videos.created_at')
                ->orderByDesc('videos.id');
        } else {
            foreach ($orderBy as $column => $direction) {
                $modelBuilder->orderBy($column, $direction);
            }
        }

        $where = array_merge(
            $this->builderWhereArray(),
            $this->listAjaxWhere()
        );

//        trigger_log("av list where...".var_export($where, true));
        if (!empty($where)) {
            $rtn = [];
            foreach ($where as $val)
            {
                if($val[0] == 'title')
                {
                    $modelBuilder->whereRaw("( title like '{$val[2]}' ')");
                }elseif($val[0] == 'theme_id'){
                    if($val[2] != '__undefined__')
                    {
                        $themeId = $val[2] ;
                    }
                } else{
                    array_push($rtn, $val);
                }
            }
            $where = $rtn;
//            trigger_log("video list ...where ".var_export($where, true));
        }
        // web 类型视频列表加入条件
        array_push($where, ['video_type', '=', 2]);
        $modelBuilder->select(['videos.*'])->where($where);

//        trigger_log("video ... sql...".var_export($modelBuilder->toSql(), true));
        /** @var \Illuminate\Database\Eloquent\Model $modelBuilder */

        list($limit, $offset) = self::limitOffsetByGet();
        $oldBuilder = clone $modelBuilder;
        $data = $modelBuilder->limit($limit)->offset($offset)->get()->map($this->listAjaxIteration());
        $data = $data->toArray();

        foreach ($data as &$v){

            $actorName = $tagName = $themeName = '';
            if(!empty($v['theme_ids']))
            {
                $theme     = AvThemeModel::whereIn('id', explode(',', $v['theme_ids']))->select(['id', 'name'])->get();
                $theme     = to_array($theme);
                $themeName = !empty($theme) ? implode(',', array_column($theme, 'name')) : '';
            }

            if(!empty($v['tag_ids']))
            {
                $tags    = AvTagModel::whereIn('id', explode(',', $v['tag_ids']))->select(['id', 'name'])->get();
                $tags    = to_array($tags);
                $tagName = !empty($tags) ? implode(',', array_column($tags, 'name')) : '';
            }

            if(!empty($v['actor_ids']))
            {
                $actors    = AvActorsModel::whereIn('id', explode(',', $v['actor_ids']))->select(['id', 'name'])->get();
                $actors    = to_array($actors);
                $actorName = !empty($actors) ? implode(',', array_column($actors, 'name')) : '';
            }

            $v['actor_name']           = $actorName;
            $v['theme_name']           = $themeName;
            $v['tag_name']             = $tagName;
            $v['play_url']             = $v['source_240'];
            $v['thumb_duration']       = seconds_to_time($v['duration']);
            $v['first_img_src']        = !empty($v['cover_thumb']) ? url_image($v['cover_thumb']) : '';
            $v['progress_status_desc'] = \VideosModel::STATUS_OPTIONS[$v['status']] ?? '';

            $sTime = $v['created_at'];
            $uTime = $v['updated_at'];
            $v['av_desc'] = <<<EOF
vid：{$v['_id']} <br>
主题：{$themeName} <br>
标题：{$v['title']} <br>
女优：{$actorName} <br>
时长：{$v['thumb_duration']} <br>
标签：{$tagName} <br>
EOF;

            $v['total'] = <<<EOF
点赞数：{$v['like_ct']} <br>
评论：{$v['comment_ct']} <br>
浏览(真)：{$v['view_ct']} <br>
浏览(假)：{$v['view_fct']} <br>
收藏(真)：{$v['favorite_ct']} <br>
收藏(假)：{$v['view_fct']} <br>
总历史点击数：{$v['rating']} <br>
总的播放量：{$v['play_count']} <br>
EOF;

            $v['time_desc'] = <<<EOF
创建时间：{$sTime} <br>
更新时间：{$uTime} <br>
EOF;


        }

        $result = [
            'count' => empty($data) ? 0 : $oldBuilder->count(),
            'data'  => $data,
            "msg"   => '',
            "desc"  => $this->getDesc($oldBuilder),
            'code'  => 0
        ];

        return $this->ajaxReturn($result);
    }

    /**
     * 获取对应的model名称
     * @return string
     * <AUTHOR>
     * @date 2020-01-17 18:57:38
     */
    protected function getModelClass(): string
    {
       return VideosModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     * <AUTHOR>
     * @date 2020-01-17 18:57:38
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     * <AUTHOR>
     * @date 2019-11-04 17:19:41
     */
    protected function getLogDesc(): string
    {
        return '';
    }

    public function indexAction()
    {
        $themeList  = (new AvThemeModel())::allList();
        $tagList    = (new AvTagModel())::allList();

        $this->assign('statusList', VideosModel::STATUS_OPTIONS);
        $this->assign('themeList', $themeList);
        $this->assign('themes', json_encode($themeList));

        array_unshift($themeList, ['name' => '无主题', 'id' => 0]);
        $themeAll   = array_column($themeList, 'name', 'id');
        $this->assign('themeSelect', $themeAll);
        $this->assign('tagList', $tagList);

        return $this->display();
    }

    /**
     * 刷新web列表缓存
     * @return void
     */
    public function refreshCacheAction()
    {
        try {
            $id    = $_POST['id'] ?? '';

            $this->ajaxSuccessMsg("刷新成功");

        } catch (Throwable $e) {

            $this->ajaxError($e->getMessage());
        }
    }


    public function saveAfterCallback($model , $oldModel = null)
    {
    }

    public function deleteAfterCallback($model ,$isDelete)
    {
        if($isDelete === false)
        {
            return false;
        }


    }


    public function gptAction()
    {
        try {
            $content  = $_POST['content'] ?? '';
            test_assert($content, '提问内容不存在');

            $key = 'web:video:gpt';
            if(redis()->hExists($key, $content)){
                $text = redis()->hGet($key, $content);
            }else{
                $result = \LibChatGpt::getCohereChatResponse($content);
                $text   = $result['text'] ?? '';
                if(!empty($text))
                {
                    \tools\RedisService::hSet($key, $content, $text);
                }
            }
            $this->ajaxSuccessMsg("获取成功", 0, ['text' => $text]);
        } catch (Throwable $e) {
            $this->ajaxError($e->getMessage());
        }
    }

    public function saveAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();

        $actorName         = !empty($post['actor_name']) ? explode(',', $post['actor_name']) : [];
        $tagName           = !empty($post['tag_name']) ? explode(',', $post['tag_name']) : [];
        $post['actor_ids'] = \service\VideoService::bindVideoRel($actorName, new AvActorsModel());
        $post['tag_ids']   = \service\VideoService::bindVideoRel($tagName, new AvActorsModel());
        unset($post['actor_name'], $post['tag_name']);

        try {
            if ($model = $this->doSave($post)) {
                return $this->ajaxSuccessMsg('操作成功', 0, call_user_func($this->listAjaxIteration(),$model));
            } else {
                return $this->ajaxError('操作错误');
            }
        } catch (\Throwable $e) {
            trigger_log($e);
            return $this->ajaxError($e->getMessage());
        }
    }

    public function saveBranchAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();

        if(empty($post['theme_id']) || !is_array($post['theme_id']))
        {
            return $this->ajaxError('参数异常');
        }

        $themeIds = implode(',', $post['theme_id']);
        $ids      = explode(',', $post['_pk']);
        try {
            if (VideosModel::whereIn('id', $ids)->update(['theme_ids' => $themeIds])) {
                return $this->ajaxSuccessMsg('操作成功');
            } else {
                return $this->ajaxError('操作错误');
            }
        } catch (\Throwable $e) {
            trigger_log($e);
            return $this->ajaxError($e->getMessage());
        }
    }

}
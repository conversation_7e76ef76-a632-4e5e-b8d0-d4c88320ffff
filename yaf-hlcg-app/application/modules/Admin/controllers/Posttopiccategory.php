<?php

/**
 * Class PosttopiccategoryController
 * @date 2023-05-23 04:27:59
 */
class PosttopiccategoryController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (PostTopicCategoryModel $item) {
            $item->setHidden([]);
            $item->status_str = PostTopicCategoryModel::STATUS_TIPS[$item->status];
            return $item;
        };
    }

    public function saveAfterCallback($model, $oldModel = null)
    {
        PostTopicCategoryModel::clearCache();
    }

    /**
     * 试图渲染
     * @return void
     */
    public function indexAction()
    {
        $this->display();
    }

    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
       return PostTopicCategoryModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     * @return string
     */
    protected function getLogDesc(): string {
        return '';
    }
}
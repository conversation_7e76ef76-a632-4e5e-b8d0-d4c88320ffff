<?php

/**
 * Class AvtagController
 */
class AvtagController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    /**
     * 列表数据过滤
     * <AUTHOR>
    /**
     * 获取列表数据
     */
    public function listAjaxAction()
    {
        if (!$this->getRequest()->isXmlHttpRequest()) {
            return $this->ajaxError('加载错误');
        }

        // \DB::enableQueryLog();
        $pkName = $this->getPkName();
        /** @var \Illuminate\Database\Eloquent\Builder $modelBuilder */
        $modelBuilder = $this->getModelObject();
        $orderBy = $this->listAjaxOrder();
        if (empty($orderBy)) {
            $modelBuilder->orderBy('sort', 'desc')
                ->orderBy('video_num', 'desc')
                ->orderBy('id', 'desc');
        } else {
            foreach ($orderBy as $column => $direction) {
                $modelBuilder->orderBy($column, $direction);
            }
        }

        $where = array_merge(
            $this->builderWhereArray(),
            $this->listAjaxWhere()
        );

        if (!empty($where)) {
            $modelBuilder->where($where);
        }

        /** @var \Illuminate\Database\Eloquent\Model $modelBuilder */

        list($limit, $offset) = self::limitOffsetByGet();
        $oldBuilder           = clone $modelBuilder;
        $data                 = $modelBuilder->limit($limit)->offset($offset)->get()->map($this->listAjaxIteration());
        $data                 = $data->toArray();

        foreach ($data as &$v){
            $v['created_at']    = date("Y-m-d", strtotime($v['created_at']));
        }


        $result = [
            'count' => empty($data) ? 0 : $oldBuilder->count(),
            'data'  => $data,
            "msg"   => '',
            "desc"  => $this->getDesc($oldBuilder),
            'code'  => 0
        ];
        // trigger_logger(\DB::getQueryLog());

        return $this->ajaxReturn($result);
    }

    /**
     * 获取对应的model名称
     * @return string
     * <AUTHOR>
     * @date 2020-01-17 18:57:38
     */
    protected function getModelClass(): string
    {
       return AvTagModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     * <AUTHOR>
     * @date 2020-01-17 18:57:38
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     * <AUTHOR>
     * @date 2019-11-04 17:19:41
     */
    protected function getLogDesc(): string
    {
        return '';
    }

    public function indexAction()
    {
        return $this->display();
    }

    /**
     * 首页显示
     * @return void
     */
    public function showIndexAction()
    {
        try {
            $id      = $_POST['id'] ?? '';
            $isIndex = $_POST['is_index'] ?? 0;
            $model  = new AvTagModel();
            $result = $model::whereIn("id", explode(',', $id))->get();
            if(empty($result))
            {
                test_assert($result, '数据不存在');
            }

            $isOk = $model::whereIn("id", explode(',', $id))
                ->update(['is_index' => $isIndex]);

            test_assert($isOk, '系统异常');

            $model::clearCache();

            $this->ajaxSuccessMsg("刷新成功");

        } catch (Throwable $e) {

            $this->ajaxError($e->getMessage());
        }
    }


    public function saveAfterCallback($model , $oldModel = null)
    {
        $model  = new AvTagModel();
        $model::clearCache();
    }

    public function deleteAfterCallback($model ,$isDelete)
    {
        $model  = new AvTagModel();
        $model::clearCache();
    }
}
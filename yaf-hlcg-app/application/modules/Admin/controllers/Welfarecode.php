<?php

class WelfarecodeController extends BackendBaseController
{
    use \repositories\HoutaiRepository;

    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (WelfareCodeModel $item) {
            $products = WelfareCodeModel::PRODUCT_OPTIONS;
            $products = array_column($products, 'name', 'id');
            $item->product_str = $products[$item->product_id];
            $item->status_str = WelfareCodeModel::STATUS_TIPS[$item->status] ?? '未知';
            return $item;
        };
    }

    /**
     * 试图渲染
     * @return string
     */
    public function indexAction()
    {
        $products = WelfareCodeModel::PRODUCT_OPTIONS;
        $products = array_column($products, 'name', 'id');
        $this->assign('products', $products);
        $this->display();
    }

    private function defendWelfareCodeCache($product_id)
    {
        $key = cached(WelfareCodeModel::CK_WELFARECODE_LIST)->generateKeyname();
        CacheKeysModel::where('key', $key)->delete();
        redis()->del($key);
        //更新库存
        $quantity = WelfareCodeModel::query()->where(['product_id' => $product_id, 'status' => WelfareCodeModel::STATUS_0])->count();
        ProductModel::query()->where('id', $product_id)->update(['quantity' => $quantity]);

    }

    protected function deleteAfterCallback($model, $isDelete)
    {
        if ($model && $isDelete) {
            $this->defendWelfareCodeCache($model->product_id);
        }
    }

    protected function saveAfterCallback($model, $oldModel = NULL)
    {
        if ($model) {
            $this->defendWelfareCodeCache($model->product_id);
        }
    }


    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
        return WelfareCodeModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     * @return string
     * <AUTHOR>
     */
    protected function getLogDesc(): string
    {
        return '';
    }
}
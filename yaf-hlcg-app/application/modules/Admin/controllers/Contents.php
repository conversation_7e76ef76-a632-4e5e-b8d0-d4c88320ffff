<?php

/**
 * Class ContentsController
 * <AUTHOR>
 * @date 2022-11-03 09:30:57
 */
class ContentsController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (ContentsModel $item) {
            $item->loadTagWithCategory();
            //category
            $category = $item->category;
            $item->category_ids = array_column($category->toArray(), 'mid');
            //tags
            $tags = $item->tags;
            $tags = array_column($tags->toArray(), 'name');
            $item->tags_str = implode(',', $tags);
            //banner和热搜
            $item->load('fields');
            $item->hotSearch = 0;
            $item->banner = '';
            collect($item->fields)->map(function ($field) use (&$item){
                if ($field->name == 'banner'){
                    $item->banner = url_image(parse_url($field->str_value, PHP_URL_PATH));
                }
                if ($field->name == 'hotSearch'){
                    $item->hotSearch = $field->str_value;
                }
            });

            // 创建一个 DateTime 对象，设定时区为 GMT
            $date = new DateTime($item->created, new DateTimeZone('GMT'));
            $date->setTimezone(new DateTimeZone('Asia/Shanghai'));
            // 格式化输出为北京时间的字符串
            $item->created = $date->format('Y-m-d H:i:s');
            $item->view_str = $item->getRawOriginal('view');

            $item->setHidden([]);
            return $item;
        };
    }

    public function delAllAction()
    {
    }
    public function delAction()
    {
    }

    /**
     * 试图渲染
     * @return void
     */
    public function indexAction()
    {
        $this->assign('get' , $_GET);
        $list = MetasModel::query()
            ->selectRaw('mid as id, name')
            ->where('type', MetasModel::TYPE_CATEGORY)
            ->orderBy('order')
            ->get()->toArray();
        $theme_json = json_encode($list, JSON_FORCE_OBJECT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        $this->assign('theme_json', $theme_json);
        $this->display();
    }

    public function app_hideAction()
    {
        $id = $_POST['id'];
        $content = ContentsModel::find($id);
        if ($content->app_hide == ContentsModel::APP_HIDE_NO) {
            $content->app_hide = ContentsModel::APP_HIDE_YES;
        } else {
            $content->app_hide = ContentsModel::APP_HIDE_NO;
        }
        $content->save();
        $this->ajaxSuccessMsg('操作成功');
    }

    public function web_showAction()
    {
        $id = $_POST['id'];
        $content = ContentsModel::find($id);
        if ($content->web_show == ContentsModel::WEB_SHOW_NO) {
            $content->web_show = ContentsModel::WEB_SHOW_YES;
        } else {
            $content->web_show = ContentsModel::WEB_SHOW_NO;
        }
        $content->save();
        $this->ajaxSuccessMsg('操作成功');
    }

    public function setTypeAction()
    {
        try {
            $cid = $_POST['cid'];
            $type = $_POST['type'];
            $sid = $_POST['sid'];
            transaction(function () use ($cid, $type, $sid){
                $content = ContentsModel::find($cid);
                test_assert($content, '文章不存在');
                if ($type == ContentsModel::TYPE_SKITS){
                    $skits = SkitsModel::find($sid);
                    test_assert($skits, '短剧合集不存在');
                    $field = FieldsModel::where('cid', $cid)->where('name', 'skits')->first();
                    if (!empty($field)){
                        $field->int_value = $sid;
                    }else{
                        $field = FieldsModel::make();
                        $field->cid = $cid;
                        $field->name = 'skits';
                        $field->type = 'int';
                        $field->int_value = $sid;
                        $field->str_value = 0;
                        $field->float_value = 0;
                    }
                    $isOK = $field->save();
                    test_assert($isOK, '短剧设置失败');
                }elseif ($type == ContentsModel::TYPE_BIG_WENT){
                    $bigEvent = BigEventModel::find($sid);
                    test_assert($bigEvent, '大事件不存在');
                    $field = FieldsModel::where('cid', $cid)->where('name', 'bigEvent')->first();
                    if (!empty($field)){
                        $field->int_value = $sid;
                    }else{
                        $field = FieldsModel::make();
                        $field->cid = $cid;
                        $field->name = 'bigEvent';
                        $field->type = 'int';
                        $field->int_value = $sid;
                        $field->str_value = 0;
                        $field->float_value = 0;
                    }
                    $isOK = $field->save();
                    test_assert($isOK, '大事件设置失败');
                }
                $content->type = $type;
                if (in_array($this, [ContentsModel::TYPE_SKITS, ContentsModel::TYPE_BIG_WENT])){
                    $content->web_show = ContentsModel::WEB_SHOW_NO;
                    $content->app_hide = ContentsModel::APP_HIDE_NO;
                }
                $content->save();
            });
            $this->ajaxSuccessMsg('操作成功');
        }catch (Throwable $e){
            $this->ajaxError($e->getMessage());
        }
    }

    public function batchSetStatusAction(){
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();
        $ary = explode(',', $post['pks_'] ?? '');
        $ary = array_filter($ary);
        $status = $post['status'];
        try {
            transaction(function () use ($ary, $status) {
                ContentsModel::whereIn('cid', $ary)->get()->map(function (ContentsModel $item) use ($status){
                    $item->status = $status;
                    $isOk = $item->save();
                    test_assert($isOk, '状态更新失败');
                });
            });
            return $this->ajaxSuccessMsg('操作成功');
        }catch (Exception $exception){
            return $this->ajaxError($exception->getMessage());
        }
    }

    public function specialEditAction()
    {
        try {
            $cid = $_POST['cid'];
            $title = $_POST['title'];
            $created = $_POST['created'];
            $category_ids = $_POST['category_ids'];
            $tags = $_POST['tags'];
            $banner = $_POST['banner'];
            $hotSearch = $_POST['hotSearch'];
            transaction(function () use ($cid, $title, $created, $category_ids, $tags, $banner, $hotSearch){
                $content = ContentsModel::find($cid);
                test_assert($content, '文章不存在');
                $content->created = strtotime($created);
                $content->title = $title;
                $isOk = $content->save();
                test_assert($isOk, '文章保存失败');
                if (!$content->relationLoaded('relationships')){
                    $content->load([ 'relationships' => function ($query) {
                        $query->with('meta');
                    },
                    ]);
                }
                foreach ($content->relationships as $relationship) {
                    $meta = $relationship->meta;
                    if ($meta->count > 0){
                        $meta->decrement('count');
                    }
                }

                //先删除关系
                RelationshipsModel::where('cid', $content->cid)->delete();
                foreach ($category_ids as $mid){
                    DB::table('relationships')->insert([
                        'mid' => $mid,
                        'cid' => $content->cid,
                    ]);
                }

                $tags = str_replace("，",',' , $tags);
                $tags = collect(explode(',',$tags))->filter(function ($tag){
                    $tag = trim($tag);
                    return !empty($tag);
                })->values();
                $tagsItems = MetasModel::useWritePdo()->where('type', MetasModel::TYPE_TAG)->whereIn('slug',$tags)->get();
                $diff = $tags->diff($tagsItems->pluck('slug'));
                foreach ($diff as $tag){
                    $meta  = MetasModel::create([
                        'name'   => $tag,
                        'slug'   => $tag,
                        'type'   => MetasModel::TYPE_TAG,
                        'count'  => 0,
                    ]);
                    $tagsItems->add($meta);
                }
                if ($tagsItems->count()){
                    MetasModel::whereIn('mid' , $tagsItems->pluck('mid'))->increment('count');
                    foreach ($tagsItems as $item){
                        DB::table('relationships')->insert([
                            'mid' => $item->mid,
                            'cid' => $content->cid,
                        ]);
                    }
                }
                //修改banner 和 热搜
                if ($banner){
                    $banner = 'https://www.51cg1.com' . parse_url($banner, PHP_URL_PATH);
                }
                $bannerModel = FieldsModel::where('cid', $content->cid)->where('name', 'banner')->first();
                if (empty($bannerModel)){
                    $bannerModel = FieldsModel::make();
                    $bannerModel->cid = $content->cid;
                    $bannerModel->name = 'banner';
                    $bannerModel->type = 'str';
                    $bannerModel->str_value = $banner;
                }else{
                    $bannerModel->str_value = $banner;
                }
                $bannerModel->save();

                $hotSearchModel = FieldsModel::where('cid', $content->cid)->where('name', 'hotSearch')->first();
                if (empty($hotSearchModel)){
                    $hotSearchModel = FieldsModel::make();
                    $hotSearchModel->cid = $content->cid;
                    $hotSearchModel->name = 'hotSearch';
                    $hotSearchModel->type = 'str';
                    $hotSearchModel->str_value = $hotSearch;
                }else{
                    $hotSearchModel->str_value = $hotSearch;
                }
                $hotSearchModel->save();
            });
            $this->ajaxSuccessMsg('操作成功');
        }catch (Throwable $e){
            $this->ajaxError($e->getMessage());
        }
    }

    public function clear_by_idAction()
    {
        $id = $_POST['id'];
        cached("content-$id")->clearCached();
        $this->ajaxSuccessMsg('操作成功');
    }

    protected function getModelObject()
    {
        return ContentsModel::whereIn('type' , [ContentsModel::TYPE_PAGE , ContentsModel::TYPE_POST, ContentsModel::TYPE_SKITS, ContentsModel::TYPE_BIG_WENT]);
    }


    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
       return ContentsModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'cid';
    }

    /**
     * 定义数据操作日志
     * @return string
     * <AUTHOR>
     */
    protected function getLogDesc(): string {
        return '';
    }
}
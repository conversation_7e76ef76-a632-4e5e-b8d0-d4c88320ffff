<?php

/**
 * Class PlateController
 * @date 2023-09-29 08:57:01
 */
class PlateController extends BackendBaseController
{
    use \repositories\HoutaiRepository;
    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        $parentplates  = \PlateModel::listPlates([],0);
        $options = [];
        foreach ($parentplates as $row){
            $options[$row["id"]] = $row["name"];
        }
        return function (PlateModel $item) use($options) {
            $item->setHidden([]);
            $item->pname = isset($options[$item->p_id]) ? $options[$item->p_id] : "一级分类";
            $item->hot_str = PlateModel::HOT_OPTIONS[$item->is_hot];
            return $item;
        };
    }

    /**
     * 试图渲染
     * @return void
     */
    public function indexAction()
    {
        $parentplates  = \PlateModel::listPlates([],0);
        $options = [];
        foreach ($parentplates as $row){
            $options[$row["id"]] = $row["name"];
        }
        $this->assign("parentplates",$options);
        $this->display();
    }
    public function saveAfterCallback($model,$oldModel=null)
    {
        // 清理一级
        $cacheKey = sprintf(\PlateModel::CK_PLATE_LIST,0);
        cached($cacheKey)->clearCached();
        if ($model->p_id){
            $cacheKey = sprintf(\PlateModel::CK_PLATE_LIST, $model->p_id);
            cached($cacheKey)->clearCached();
        }
        if ($oldModel && $oldModel->p_id){
            $cacheKey = sprintf(\PlateModel::CK_PLATE_LIST, $oldModel->p_id);
            cached($cacheKey)->clearCached();
        }
    }
    //*** 分类删除  删除该分类下的视频分类关联关系
    public function deleteAfterCallback($model,$delete=1)
    {
        \VideosPlatesModel::where('plate_id', $model->id)->delete();
    }

    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
       return PlateModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     * @return string
     */
    protected function getLogDesc(): string {
        return '';
    }
}
<?php
$a=1;
$b=4;
echo $a+$b;
die;
echo xdebug_info();
// phpinfo();
die;
ini_set('date.timezone', 'Asia/Shanghai');
function make_sign($array, $signKey): string
{
    if (empty($array)) {
        return '';
    }
    ksort($array);
    //$string = http_build_query($array);

    $arr_temp = [];
    foreach ($array as $key => $val) {
        if ($key == 'data') {
            $valTemp = str_replace(' ', '+', $val);
            $arr_temp[] = $key . '=' . $valTemp;
        } else {
            $arr_temp[] = $key . '=' . $val;
        }
    }
    $string = implode('&', $arr_temp);

    $string = $string . $signKey;

    #先sha256签名 再md5签名
    $sign_str = md5(hash('sha256', $string));

    // trigger_error('string:'.$string);

    return $sign_str;
}


function makeCallBackPaySign($data, $sign = 'scb37537f85ext23766194765b9epa51'): string
{
    if (isset($data['sign'])) {
        unset($data['sign']);
    }
    ksort($data);
    $string = '';
    $string = http_build_query($data, null, '&');
    return md5($string . $sign);
}


function gotoApi($url, $params = [], $header = [])
{
    $ch = curl_init();
    $headers = [];

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 500);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    $header = ['Content-Type: multipart/form-data'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLINFO_HEADER_OUT, true);
    curl_setopt($ch, CURLOPT_HEADERFUNCTION,
        function ($curl, $header) use (&$headers) {
            $len = strlen($header);
            $header = explode(':', $header, 2);
            if (count($header) < 2) // ignore invalid headers
                return $len;

            $headers[strtolower(trim($header[0]))] = trim($header[1]);

            return $len;
        }
    );
    
    echo "正在发送POST请求到: " . $url . PHP_EOL;
    echo "POST参数: " . PHP_EOL;
    print_r($params);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        echo "cURL错误: " . curl_error($ch) . PHP_EOL;
    }
    
    echo "cURL信息:" . PHP_EOL;
    print_r(curl_getinfo($ch));
    
    curl_close($ch);
    return $response;
}


// ini_set("error_reporting",E_ALL);

//$oauthId = md5(mt_rand());
//echo $oauthId.PHP_EOL;
$oauthId = md5('123');
//echo $oauthId.PHP_EOL;die();
//$sign = 'scb37537f85spaycm59f7e318b9epa51';
$signKey = 'cb7c22174dc30d664784e6dw22fd664d';
const KEY = 'b26243c9efcd5524';
const IV = '66fe2be26eeeb65b';

$data = [
    'oauth_type' => 'web',
    'oauth_id'   => md5('abc110'),//$sign,//'abc1101',
    'bundleId'   => 'com.xekoko.xxx',
    //'token'      => 'F8EC694B741997C0A1429288EFB522E7616408D0B29F2B431C62B0DC063367F4AC30BDE9E822D32D356CAE9A73574E9A930D5DDB82A7F92C5ECE8829942DC9C52C2FEAA81A3BB9EC40962584E32AAE2EA4EA388AA165DCA9A0F4003727974103B44B396583EB19AFF563E1BA4E947DF246868E',
    'version'    => '1.4.1',
];
$other = [
    'sort'  => 'new',
    'id' => 22233,
    // 'page'  => 1,
    // 'limit' => 2,
    'text' => '我是评论内容',
    'word' => '昔日女神',
];
echo '<pre>';
echo '参数:' . PHP_EOL;
echo json_encode($other, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
$data = array_merge($data, $other);

echo "加密前原始参数:" . PHP_EOL;
print_r($data);
$encData = json_encode($data);
echo $encData . PHP_EOL;

$xData = openssl_encrypt($encData, 'aes-128-cbc', KEY, 0, IV);
$callData = [
    'data'      => $xData,
    'timestamp' => time(),
    '_ver'      => 'v0',
];
$callData['sign'] = make_sign($callData, $signKey);
echo "加密后请求参数:" . PHP_EOL;
print_r($callData);
echo PHP_EOL;

https://bpi1.lbqwpwwt.top/api.php
// $url = "http://51.local/api.php/api/index/index";
// $url = "http://51.local/api.php/api/material/list_sort";
// $url = "http://51.local/api.php/api/material/list";
// $url = "http://51.local/api.php/api/material/favorite";
// $url = "http://51.local/api.php/api/material/list_favorite";
// $url = "http://51.local/api.php/api/material/list_rec";
$url = "http://hlcg.local/api.php/api/home/<USER>";
// $url = "http://51.local/api.php/api/material/comment";
// $url = "http://51.local/api.php/api/material/list_comment";
// $url = "http://51.local/api.php/api/material/like_comment";
// $url = "http://51.local/api.php/api/material/search";
// $url = "http://51.local/api.php/api/material/buy";
// $url = "http://51.local/api.php/api/user/userInfo";
// $url = "http://51.local/api.php/api/user/test";
// $url = "http://51.local/api.php/api/mv/getDetail";
// $url = "http://51.local/api.php/api/community/post_detail";
// $url = "http://51.local/api.php/api/element/getElementById";
//$url = "https://bpi3.lbqwpwwt.top/api.php/api/home/<USER>";
//$url = "https://bpi2.lbqwpwwt.top/api.php//api/home/<USER>";
//$url = "https://bpi1.lbqwpwwt.top/api.php/api/home/<USER>";
//$url = "https://bpi2.kodsxhn.xyz/api.php/api/user/userInfo";
//$url = "https://bpi3.kodsxhn.xyz/api.php/api/user/userInfo";
//$url = "https://api1.kodsxhn.xyz/api.php/api/user/userInfo";
 
$ret = gotoApi($url, $callData);
echo "<pre>";  
echo "API响应:" . PHP_EOL;
var_dump($ret);
echo PHP_EOL;
$ret = json_decode($ret, true);
print_r($ret);
//file_put_contents('1.txt', $ret['data']);
// $rre = openssl_decrypt($ret['data'], 'aes-128-cbc', KEY, 0, IV);
//$rre = openssl_decrypt($ret['data'], 'aes-128-cbc', '58df15468c69fa8a', 0, '27f89b88b0c34cbb');
// echo PHP_EOL;
// echo '返回数据' . PHP_EOL;
// file_put_contents('cs.php', $rre);
// print_r(json_decode($rre, true));
// echo PHP_EOL;
